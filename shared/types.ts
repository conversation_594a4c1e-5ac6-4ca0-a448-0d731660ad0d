// 昆虫类型
export enum InsectType {
  COCKROACH = 'cockroach',
  BEETLE = 'beetle'
}

// 昆虫生命阶段
export enum LifeStage {
  EGG = 'egg',
  LARVA = 'larva',
  PUPA = 'pupa',
  ADULT = 'adult'
}

// 昆虫性别
export enum Gender {
  MALE = 'male',
  FEMALE = 'female'
}

// 昆虫数据接口
export interface Insect {
  id: string;
  type: InsectType;
  stage: LifeStage;
  gender: Gender;
  age: number; // 以小时为单位
  health: number; // 0-100
  hunger: number; // 0-100
  happiness: number; // 0-100
  breedingBoxId: string;
  parentIds?: string[];
  bornAt: Date;
  lastFed: Date;
  price: number; // 市场价值
}

// 养殖箱接口
export interface BreedingBox {
  id: string;
  name: string;
  capacity: number;
  currentCount: number;
  temperature: number; // 摄氏度
  humidity: number; // 百分比
  cleanliness: number; // 0-100
  foodLevel: number; // 0-100
  insects: string[]; // 昆虫ID数组
  purchasePrice: number;
  maintenanceCost: number; // 每小时维护费用
}

// 玩家数据接口
export interface Player {
  id: string;
  name: string;
  money: number;
  experience: number;
  level: number;
  breedingBoxes: string[];
  insects: string[];
  achievements: string[];
  lastLogin: Date;
}

// 游戏统计接口
export interface GameStats {
  totalInsects: number;
  totalEggs: number;
  totalSales: number;
  totalRevenue: number;
  breedingBoxCount: number;
}

// 商店物品接口
export interface ShopItem {
  id: string;
  name: string;
  description: string;
  price: number;
  type: 'breedingBox' | 'food' | 'medicine' | 'decoration';
  properties: Record<string, any>;
}

// WebSocket消息类型
export enum MessageType {
  GAME_STATE_UPDATE = 'gameStateUpdate',
  INSECT_UPDATE = 'insectUpdate',
  BREEDING_BOX_UPDATE = 'breedingBoxUpdate',
  PLAYER_UPDATE = 'playerUpdate',
  BREEDING_EVENT = 'breedingEvent',
  SALE_EVENT = 'saleEvent',
  PURCHASE_EVENT = 'purchaseEvent'
}

// WebSocket消息接口
export interface GameMessage {
  type: MessageType;
  data: any;
  timestamp: Date;
}

// API响应接口
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}
