# 昆虫养殖游戏

一个基于Three.js和Node.js的3D昆虫养殖模拟游戏。

## 功能特性

- 🐛 **昆虫养殖**: 养殖蟑螂和甲虫，观察它们的生命周期
- 🥚 **生命周期**: 从卵到幼虫、蛹，最后成为成虫的完整生命周期
- 💰 **经济系统**: 通过出售昆虫赚取金币，购买更多养殖设备
- 🏠 **养殖箱管理**: 购买和管理多个养殖箱，每个都有不同的容量和特性
- 🍽️ **喂食系统**: 定期喂食昆虫以保持它们的健康和快乐
- 💕 **繁殖系统**: 让成虫繁殖产卵，扩大你的昆虫群体
- 📊 **实时统计**: 监控昆虫的健康、饥饿度和快乐度
- 🎮 **3D交互**: 使用Three.js构建的沉浸式3D环境

## 技术栈

### 后端
- Node.js + TypeScript
- Express.js (REST API)
- Socket.io (实时通信)
- SQLite (数据存储)

### 前端
- Three.js (3D渲染)
- TypeScript
- Vite (构建工具)
- 原生HTML/CSS/JS

## 安装和运行

### 前置要求
- Node.js (版本 16 或更高)
- npm 或 yarn

### 安装步骤

1. **安装依赖**
   ```bash
   # 安装根目录依赖
   npm install
   
   # 安装所有子项目依赖
   npm run install:all
   ```

2. **启动开发服务器**
   ```bash
   # 同时启动前端和后端开发服务器
   npm run dev
   ```
   
   或者分别启动：
   ```bash
   # 启动后端服务器 (端口 3001)
   npm run dev:server
   
   # 启动前端开发服务器 (端口 3000)
   npm run dev:client
   ```

3. **访问游戏**
   打开浏览器访问 `http://localhost:3000`

### 生产环境部署

1. **构建项目**
   ```bash
   npm run build
   ```

2. **启动生产服务器**
   ```bash
   npm start
   ```

## 游戏玩法

### 基础操作
1. **开始游戏**: 新玩家会获得一个初始养殖箱和一对蟑螂
2. **喂食昆虫**: 点击"喂食"按钮为昆虫提供食物（消耗10金币）
3. **观察成长**: 昆虫会经历卵→幼虫→蛹→成虫的生命周期
4. **繁殖昆虫**: 选择一对健康的成虫进行繁殖
5. **出售昆虫**: 出售成虫获得金币和经验值
6. **购买设备**: 使用金币购买更大容量的养殖箱

### 游戏机制
- **生命周期时间**:
  - 卵: 24小时孵化
  - 幼虫: 72小时化蛹
  - 蛹: 120小时羽化为成虫
- **健康系统**: 昆虫需要定期喂食，否则健康度下降
- **经济系统**: 成虫价值更高，健康状况影响售价
- **升级系统**: 获得经验值提升等级，解锁更多功能

### 3D交互
- **鼠标控制**: 拖拽旋转视角，滚轮缩放
- **点击交互**: 点击昆虫查看详细信息
- **养殖箱管理**: 点击养殖箱查看状态

## 开发说明

### 项目结构
```
├── client/                 # 前端代码
│   ├── src/
│   │   ├── game/          # 游戏引擎
│   │   ├── ui/            # UI管理
│   │   ├── network/       # 网络通信
│   │   └── main.ts        # 入口文件
│   └── index.html         # HTML模板
├── server/                # 后端代码
│   ├── src/
│   │   ├── game/          # 游戏逻辑
│   │   ├── database/      # 数据库管理
│   │   ├── routes/        # API路由
│   │   └── app.ts         # 服务器入口
└── shared/                # 共享类型定义
    └── types.ts
```

### FBX模型
游戏支持FBX格式的3D模型。将你的昆虫模型文件放在 `client/public/models/` 目录下：
- `cockroach.fbx` - 蟑螂模型
- `beetle.fbx` - 甲虫模型

### 数据库
游戏使用SQLite数据库存储游戏数据，数据库文件会自动创建在项目根目录的 `game.db`。

## 贡献

欢迎提交Issue和Pull Request来改进游戏！

## 许可证

MIT License
