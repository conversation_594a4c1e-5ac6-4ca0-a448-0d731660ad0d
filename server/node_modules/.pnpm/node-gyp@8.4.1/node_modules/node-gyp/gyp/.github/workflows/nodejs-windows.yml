name: Node.js Windows integration

on: [push, pull_request]

jobs:
  build-windows:
    runs-on: windows-latest
    steps:
      - name: Clone gyp-next
        uses: actions/checkout@v2
        with:
          path: gyp-next
      - name: Clone nodejs/node
        uses: actions/checkout@v2
        with:
          repository: nodejs/node
          path: node
      - name: Install deps
        run: choco install nasm
      - name: Replace gyp in Node.js
        run: |
          rm -Recurse node/tools/gyp
          cp -Recurse gyp-next node/tools/gyp
      - name: Build Node.js
        run: |
          cd node
          ./vcbuild.bat
