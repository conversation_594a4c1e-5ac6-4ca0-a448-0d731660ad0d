{"version": 3, "file": "rimraf-manual.js", "sourceRoot": "", "sources": ["../../src/rimraf-manual.ts"], "names": [], "mappings": ";;;;;;AAAA,gEAAoC;AAEpC,uDAAgE;AAChE,2DAAsE;AAEzD,QAAA,YAAY,GAAG,qBAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,iCAAa,CAAC,CAAC,CAAC,6BAAW,CAAA;AACjE,QAAA,gBAAgB,GAC3B,qBAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,qCAAiB,CAAC,CAAC,CAAC,iCAAe,CAAA", "sourcesContent": ["import platform from './platform.js'\n\nimport { rimrafPosix, rimrafPosixSync } from './rimraf-posix.js'\nimport { rimrafWindows, rimrafWindowsSync } from './rimraf-windows.js'\n\nexport const rimrafManual = platform === 'win32' ? rimrafWindows : rimrafPosix\nexport const rimrafManualSync =\n  platform === 'win32' ? rimrafWindowsSync : rimrafPosixSync\n"]}