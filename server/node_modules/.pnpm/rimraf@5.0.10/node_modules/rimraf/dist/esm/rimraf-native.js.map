{"version": 3, "file": "<PERSON><PERSON>f-native.js", "sourceRoot": "", "sources": ["../../src/rimraf-native.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,SAAS,CAAA;AAC1C,MAAM,EAAE,EAAE,EAAE,GAAG,QAAQ,CAAA;AAEvB,MAAM,CAAC,MAAM,YAAY,GAAG,KAAK,EAC/B,IAAY,EACZ,GAAuB,EACL,EAAE;IACpB,MAAM,EAAE,CAAC,IAAI,EAAE;QACb,GAAG,GAAG;QACN,KAAK,EAAE,IAAI;QACX,SAAS,EAAE,IAAI;KAChB,CAAC,CAAA;IACF,OAAO,IAAI,CAAA;AACb,CAAC,CAAA;AAED,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAC9B,IAAY,EACZ,GAAsB,EACb,EAAE;IACX,MAAM,CAAC,IAAI,EAAE;QACX,GAAG,GAAG;QACN,KAAK,EAAE,IAAI;QACX,SAAS,EAAE,IAAI;KAChB,CAAC,CAAA;IACF,OAAO,IAAI,CAAA;AACb,CAAC,CAAA", "sourcesContent": ["import { RimrafAsyncOptions, RimrafSyncOptions } from './index.js'\nimport { promises, rmSync } from './fs.js'\nconst { rm } = promises\n\nexport const rimrafNative = async (\n  path: string,\n  opt: RimrafAsyncOptions,\n): Promise<boolean> => {\n  await rm(path, {\n    ...opt,\n    force: true,\n    recursive: true,\n  })\n  return true\n}\n\nexport const rimrafNativeSync = (\n  path: string,\n  opt: RimrafSyncOptions,\n): boolean => {\n  rmSync(path, {\n    ...opt,\n    force: true,\n    recursive: true,\n  })\n  return true\n}\n"]}