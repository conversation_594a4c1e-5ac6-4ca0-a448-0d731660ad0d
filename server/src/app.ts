import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import cors from 'cors';
import path from 'path';
import { GameManager } from './game/GameManager';
import { DatabaseManager } from './database/DatabaseManager';
import { setupRoutes } from './routes';

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 3001;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../../client/dist')));

// 初始化数据库
const dbManager = new DatabaseManager();
const gameManager = new GameManager(io, dbManager);

// 设置路由
setupRoutes(app, gameManager);

// Socket.IO 连接处理
io.on('connection', (socket) => {
  console.log('用户连接:', socket.id);
  
  // 加入游戏
  socket.on('joinGame', async (playerId: string) => {
    try {
      await gameManager.addPlayer(socket.id, playerId);
      socket.emit('gameJoined', { success: true });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      socket.emit('gameJoined', { success: false, error: errorMessage });
    }
  });

  // 断开连接
  socket.on('disconnect', () => {
    console.log('用户断开连接:', socket.id);
    gameManager.removePlayer(socket.id);
  });

  // 购买养殖箱
  socket.on('purchaseBreedingBox', async (data) => {
    try {
      const result = await gameManager.purchaseBreedingBox(socket.id, data.boxType);
      socket.emit('purchaseResult', result);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      socket.emit('purchaseResult', { success: false, error: errorMessage });
    }
  });

  // 喂食昆虫
  socket.on('feedInsects', async (data) => {
    try {
      const result = await gameManager.feedInsects(socket.id, data.breedingBoxId);
      socket.emit('feedResult', result);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      socket.emit('feedResult', { success: false, error: errorMessage });
    }
  });

  // 繁殖昆虫
  socket.on('breedInsects', async (data) => {
    try {
      const result = await gameManager.breedInsects(socket.id, data.maleId, data.femaleId);
      socket.emit('breedResult', result);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      socket.emit('breedResult', { success: false, error: errorMessage });
    }
  });

  // 出售昆虫
  socket.on('sellInsect', async (data) => {
    try {
      const result = await gameManager.sellInsect(socket.id, data.insectId);
      socket.emit('sellResult', result);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      socket.emit('sellResult', { success: false, error: errorMessage });
    }
  });
});

// 启动游戏循环
gameManager.startGameLoop();

// 启动服务器
server.listen(PORT, () => {
  console.log(`服务器运行在端口 ${PORT}`);
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('正在关闭服务器...');
  gameManager.stopGameLoop();
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});
