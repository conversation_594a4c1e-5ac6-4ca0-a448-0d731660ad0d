import { Server } from 'socket.io';
import { v4 as uuidv4 } from 'uuid';
import { DatabaseManager } from '../database/DatabaseManager';
import { 
  Insect, 
  BreedingBox, 
  Player, 
  InsectType, 
  LifeStage, 
  Gender, 
  MessageType,
  GameMessage 
} from '../../../shared/types';

interface ConnectedPlayer {
  socketId: string;
  playerId: string;
  player: Player;
}

export class GameManager {
  private io: Server;
  private db: DatabaseManager;
  private connectedPlayers: Map<string, ConnectedPlayer> = new Map();
  private gameLoopInterval: NodeJS.Timeout | null = null;
  private readonly GAME_LOOP_INTERVAL = 60000; // 1分钟

  constructor(io: Server, db: DatabaseManager) {
    this.io = io;
    this.db = db;
  }

  async addPlayer(socketId: string, playerId: string): Promise<void> {
    let player = await this.db.getPlayer(playerId);
    
    if (!player) {
      // 创建新玩家
      player = {
        id: playerId,
        name: `玩家${playerId.slice(0, 8)}`,
        money: 1000,
        experience: 0,
        level: 1,
        breedingBoxes: [],
        insects: [],
        achievements: [],
        lastLogin: new Date()
      };
      
      await this.db.createPlayer(player);
      
      // 给新玩家一个初始养殖箱和几只昆虫
      await this.giveStarterPack(playerId);
      player = await this.db.getPlayer(playerId);
    } else {
      // 更新最后登录时间
      player.lastLogin = new Date();
      await this.db.updatePlayer(player);
    }

    this.connectedPlayers.set(socketId, {
      socketId,
      playerId,
      player: player!
    });

    // 发送初始游戏状态
    this.sendGameState(socketId);
  }

  removePlayer(socketId: string): void {
    this.connectedPlayers.delete(socketId);
  }

  private async giveStarterPack(playerId: string): Promise<void> {
    // 创建初始养殖箱
    const starterBox: BreedingBox = {
      id: uuidv4(),
      name: '初始养殖箱',
      capacity: 10,
      currentCount: 0,
      temperature: 25,
      humidity: 60,
      cleanliness: 100,
      foodLevel: 100,
      insects: [],
      purchasePrice: 0,
      maintenanceCost: 0.5
    };

    await this.db.createBreedingBox(starterBox, playerId);

    // 创建初始昆虫 - 一对蟑螂
    const maleRoach: Insect = {
      id: uuidv4(),
      type: InsectType.COCKROACH,
      stage: LifeStage.ADULT,
      gender: Gender.MALE,
      age: 24, // 1天大
      health: 100,
      hunger: 50,
      happiness: 75,
      breedingBoxId: starterBox.id,
      bornAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
      lastFed: new Date(),
      price: 50
    };

    const femaleRoach: Insect = {
      id: uuidv4(),
      type: InsectType.COCKROACH,
      stage: LifeStage.ADULT,
      gender: Gender.FEMALE,
      age: 24,
      health: 100,
      hunger: 50,
      happiness: 75,
      breedingBoxId: starterBox.id,
      bornAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
      lastFed: new Date(),
      price: 50
    };

    await this.db.createInsect(maleRoach, playerId);
    await this.db.createInsect(femaleRoach, playerId);

    // 更新养殖箱昆虫数量
    starterBox.currentCount = 2;
    starterBox.insects = [maleRoach.id, femaleRoach.id];
    await this.db.updateBreedingBox(starterBox);
  }

  async purchaseBreedingBox(socketId: string, boxType: string): Promise<any> {
    const connectedPlayer = this.connectedPlayers.get(socketId);
    if (!connectedPlayer) throw new Error('玩家未连接');

    const boxConfigs = {
      basic: { name: '基础养殖箱', capacity: 15, price: 500, maintenance: 1.0 },
      advanced: { name: '高级养殖箱', capacity: 25, price: 1200, maintenance: 1.5 },
      premium: { name: '豪华养殖箱', capacity: 40, price: 2500, maintenance: 2.0 }
    };

    const config = boxConfigs[boxType as keyof typeof boxConfigs];
    if (!config) throw new Error('无效的养殖箱类型');

    if (connectedPlayer.player.money < config.price) {
      throw new Error('金钱不足');
    }

    const newBox: BreedingBox = {
      id: uuidv4(),
      name: config.name,
      capacity: config.capacity,
      currentCount: 0,
      temperature: 25,
      humidity: 60,
      cleanliness: 100,
      foodLevel: 100,
      insects: [],
      purchasePrice: config.price,
      maintenanceCost: config.maintenance
    };

    await this.db.createBreedingBox(newBox, connectedPlayer.playerId);
    
    // 扣除金钱
    connectedPlayer.player.money -= config.price;
    connectedPlayer.player.breedingBoxes.push(newBox.id);
    await this.db.updatePlayer(connectedPlayer.player);

    this.sendGameState(socketId);
    
    return { success: true, breedingBox: newBox };
  }

  async feedInsects(socketId: string, breedingBoxId: string): Promise<any> {
    const connectedPlayer = this.connectedPlayers.get(socketId);
    if (!connectedPlayer) throw new Error('玩家未连接');

    const FEED_COST = 10;
    if (connectedPlayer.player.money < FEED_COST) {
      throw new Error('金钱不足购买食物');
    }

    const insects = await this.db.getBreedingBoxInsects(breedingBoxId);
    const now = new Date();

    for (const insect of insects) {
      insect.hunger = Math.min(100, insect.hunger + 30);
      insect.happiness = Math.min(100, insect.happiness + 10);
      insect.lastFed = now;
      await this.db.updateInsect(insect);
    }

    // 扣除费用
    connectedPlayer.player.money -= FEED_COST;
    await this.db.updatePlayer(connectedPlayer.player);

    this.sendGameState(socketId);
    
    return { success: true, message: `喂食了 ${insects.length} 只昆虫` };
  }

  async breedInsects(socketId: string, maleId: string, femaleId: string): Promise<any> {
    const connectedPlayer = this.connectedPlayers.get(socketId);
    if (!connectedPlayer) throw new Error('玩家未连接');

    const insects = await this.db.getPlayerInsects(connectedPlayer.playerId);
    const male = insects.find(i => i.id === maleId);
    const female = insects.find(i => i.id === femaleId);

    if (!male || !female) throw new Error('找不到指定的昆虫');
    if (male.gender !== Gender.MALE || female.gender !== Gender.FEMALE) {
      throw new Error('性别不匹配');
    }
    if (male.type !== female.type) throw new Error('种类不匹配');
    if (male.stage !== LifeStage.ADULT || female.stage !== LifeStage.ADULT) {
      throw new Error('只有成虫才能繁殖');
    }
    if (male.health < 70 || female.health < 70) {
      throw new Error('昆虫健康状况不佳，无法繁殖');
    }

    // 检查养殖箱容量
    const breedingBoxes = await this.db.getPlayerBreedingBoxes(connectedPlayer.playerId);
    const targetBox = breedingBoxes.find(box => box.insects.includes(maleId) || box.insects.includes(femaleId));
    
    if (!targetBox) throw new Error('找不到养殖箱');
    if (targetBox.currentCount >= targetBox.capacity) {
      throw new Error('养殖箱已满');
    }

    // 创建卵
    const eggCount = Math.floor(Math.random() * 5) + 3; // 3-7个卵
    const eggs: Insect[] = [];

    for (let i = 0; i < Math.min(eggCount, targetBox.capacity - targetBox.currentCount); i++) {
      const egg: Insect = {
        id: uuidv4(),
        type: male.type,
        stage: LifeStage.EGG,
        gender: Math.random() > 0.5 ? Gender.MALE : Gender.FEMALE,
        age: 0,
        health: 100,
        hunger: 0, // 卵不需要进食
        happiness: 100,
        breedingBoxId: targetBox.id,
        parentIds: [maleId, femaleId],
        bornAt: new Date(),
        lastFed: new Date(),
        price: 10
      };

      eggs.push(egg);
      await this.db.createInsect(egg, connectedPlayer.playerId);
    }

    // 更新养殖箱
    targetBox.currentCount += eggs.length;
    targetBox.insects.push(...eggs.map(egg => egg.id));
    await this.db.updateBreedingBox(targetBox);

    // 降低父母的健康和快乐度
    male.health = Math.max(50, male.health - 20);
    male.happiness = Math.max(30, male.happiness - 15);
    female.health = Math.max(50, female.health - 20);
    female.happiness = Math.max(30, female.happiness - 15);

    await this.db.updateInsect(male);
    await this.db.updateInsect(female);

    this.sendGameState(socketId);
    
    return {
      success: true,
      message: `成功繁殖了 ${eggs.length} 个卵`,
      eggs: eggs.length
    };
  }

  async sellInsect(socketId: string, insectId: string): Promise<any> {
    const connectedPlayer = this.connectedPlayers.get(socketId);
    if (!connectedPlayer) throw new Error('玩家未连接');

    const insects = await this.db.getPlayerInsects(connectedPlayer.playerId);
    const insect = insects.find(i => i.id === insectId);

    if (!insect) throw new Error('找不到指定的昆虫');
    if (insect.stage === LifeStage.EGG) throw new Error('卵无法出售');

    // 计算价格
    let price = insect.price;
    if (insect.stage === LifeStage.ADULT) {
      price *= 2; // 成虫价格翻倍
    }

    // 健康状况影响价格
    price = Math.floor(price * (insect.health / 100));

    // 删除昆虫
    await this.db.deleteInsect(insectId);

    // 更新养殖箱
    const breedingBoxes = await this.db.getPlayerBreedingBoxes(connectedPlayer.playerId);
    const targetBox = breedingBoxes.find(box => box.insects.includes(insectId));

    if (targetBox) {
      targetBox.currentCount -= 1;
      targetBox.insects = targetBox.insects.filter(id => id !== insectId);
      await this.db.updateBreedingBox(targetBox);
    }

    // 增加金钱和经验
    connectedPlayer.player.money += price;
    connectedPlayer.player.experience += Math.floor(price / 10);

    // 检查升级
    const newLevel = Math.floor(connectedPlayer.player.experience / 100) + 1;
    if (newLevel > connectedPlayer.player.level) {
      connectedPlayer.player.level = newLevel;
      // 升级奖励
      connectedPlayer.player.money += newLevel * 50;
    }

    await this.db.updatePlayer(connectedPlayer.player);

    this.sendGameState(socketId);

    return {
      success: true,
      message: `出售成功，获得 ${price} 金币`,
      price,
      levelUp: newLevel > (newLevel - 1)
    };
  }

  private async sendGameState(socketId: string): Promise<void> {
    const connectedPlayer = this.connectedPlayers.get(socketId);
    if (!connectedPlayer) return;

    const player = await this.db.getPlayer(connectedPlayer.playerId);
    const breedingBoxes = await this.db.getPlayerBreedingBoxes(connectedPlayer.playerId);
    const insects = await this.db.getPlayerInsects(connectedPlayer.playerId);

    const gameState = {
      player,
      breedingBoxes,
      insects,
      timestamp: new Date()
    };

    this.io.to(socketId).emit('gameStateUpdate', gameState);
  }

  startGameLoop(): void {
    if (this.gameLoopInterval) return;

    this.gameLoopInterval = setInterval(async () => {
      await this.updateGameState();
    }, this.GAME_LOOP_INTERVAL);

    console.log('游戏循环已启动');
  }

  stopGameLoop(): void {
    if (this.gameLoopInterval) {
      clearInterval(this.gameLoopInterval);
      this.gameLoopInterval = null;
      console.log('游戏循环已停止');
    }
  }

  private async updateGameState(): Promise<void> {
    for (const [socketId, connectedPlayer] of this.connectedPlayers) {
      try {
        await this.updatePlayerGameState(connectedPlayer);
        this.sendGameState(socketId);
      } catch (error) {
        console.error(`更新玩家 ${connectedPlayer.playerId} 游戏状态失败:`, error);
      }
    }
  }

  private async updatePlayerGameState(connectedPlayer: ConnectedPlayer): Promise<void> {
    const insects = await this.db.getPlayerInsects(connectedPlayer.playerId);
    const breedingBoxes = await this.db.getPlayerBreedingBoxes(connectedPlayer.playerId);

    // 更新昆虫状态
    for (const insect of insects) {
      const ageIncrease = this.GAME_LOOP_INTERVAL / (1000 * 60 * 60); // 转换为小时
      insect.age += ageIncrease;

      // 饥饿度增加
      if (insect.stage !== LifeStage.EGG) {
        insect.hunger = Math.max(0, insect.hunger - 5);

        // 饥饿影响健康和快乐
        if (insect.hunger < 20) {
          insect.health = Math.max(0, insect.health - 2);
          insect.happiness = Math.max(0, insect.happiness - 3);
        }
      }

      // 生命阶段转换
      await this.updateInsectLifeStage(insect);

      await this.db.updateInsect(insect);
    }

    // 更新养殖箱状态
    for (const box of breedingBoxes) {
      // 清洁度下降
      box.cleanliness = Math.max(0, box.cleanliness - 2);

      // 食物消耗
      if (box.currentCount > 0) {
        box.foodLevel = Math.max(0, box.foodLevel - box.currentCount);
      }

      // 维护费用
      connectedPlayer.player.money = Math.max(0, connectedPlayer.player.money - box.maintenanceCost);

      await this.db.updateBreedingBox(box);
    }

    await this.db.updatePlayer(connectedPlayer.player);
  }

  private async updateInsectLifeStage(insect: Insect): Promise<void> {
    const ageInHours = insect.age;

    switch (insect.stage) {
      case LifeStage.EGG:
        if (ageInHours >= 24) { // 1天后孵化
          insect.stage = LifeStage.LARVA;
          insect.hunger = 80; // 幼虫很饿
          insect.price = Math.floor(insect.price * 1.5);
        }
        break;

      case LifeStage.LARVA:
        if (ageInHours >= 72) { // 3天后化蛹
          insect.stage = LifeStage.PUPA;
          insect.hunger = 0; // 蛹不进食
          insect.price = Math.floor(insect.price * 1.3);
        }
        break;

      case LifeStage.PUPA:
        if (ageInHours >= 120) { // 5天后羽化
          insect.stage = LifeStage.ADULT;
          insect.hunger = 60;
          insect.price = Math.floor(insect.price * 2);
        }
        break;
    }
  }
}
