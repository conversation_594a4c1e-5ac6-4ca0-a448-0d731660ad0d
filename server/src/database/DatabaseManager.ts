import sqlite3 from 'sqlite3';
import { promisify } from 'util';
import path from 'path';
import { Insect, BreedingBox, Player } from '../../../shared/types';

// 定义数据库行类型
interface PlayerRow {
  id: string;
  name: string;
  money: number;
  experience: number;
  level: number;
  achievements: string;
  lastLogin: string;
}

interface BreedingBoxRow {
  id: string;
  playerId: string;
  name: string;
  capacity: number;
  currentCount: number;
  temperature: number;
  humidity: number;
  cleanliness: number;
  foodLevel: number;
  purchasePrice: number;
  maintenanceCost: number;
}

interface InsectRow {
  id: string;
  playerId: string;
  breedingBoxId: string;
  type: string;
  stage: string;
  gender: string;
  age: number;
  health: number;
  hunger: number;
  happiness: number;
  parentIds: string;
  bornAt: string;
  lastFed: string;
  price: number;
}

export class DatabaseManager {
  private db: sqlite3.Database;
  private run: (sql: string, params?: any[]) => Promise<sqlite3.RunResult>;
  private get: (sql: string, params?: any[]) => Promise<any>;
  private all: (sql: string, params?: any[]) => Promise<any[]>;

  constructor() {
    const dbPath = path.join(__dirname, '../../../game.db');
    this.db = new sqlite3.Database(dbPath);

    // 创建promisified方法
    this.run = promisify(this.db.run.bind(this.db));
    this.get = promisify(this.db.get.bind(this.db));
    this.all = promisify(this.db.all.bind(this.db));

    this.initDatabase();
  }

  private async initDatabase() {
    try {
      // 创建玩家表
      await this.run(`
        CREATE TABLE IF NOT EXISTS players (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          money INTEGER DEFAULT 1000,
          experience INTEGER DEFAULT 0,
          level INTEGER DEFAULT 1,
          achievements TEXT DEFAULT '[]',
          lastLogin DATETIME DEFAULT CURRENT_TIMESTAMP,
          createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // 创建养殖箱表
      await this.run(`
        CREATE TABLE IF NOT EXISTS breeding_boxes (
          id TEXT PRIMARY KEY,
          playerId TEXT NOT NULL,
          name TEXT NOT NULL,
          capacity INTEGER NOT NULL,
          currentCount INTEGER DEFAULT 0,
          temperature REAL DEFAULT 25.0,
          humidity REAL DEFAULT 60.0,
          cleanliness INTEGER DEFAULT 100,
          foodLevel INTEGER DEFAULT 100,
          purchasePrice INTEGER NOT NULL,
          maintenanceCost REAL DEFAULT 1.0,
          createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (playerId) REFERENCES players (id)
        )
      `);

      // 创建昆虫表
      await this.run(`
        CREATE TABLE IF NOT EXISTS insects (
          id TEXT PRIMARY KEY,
          playerId TEXT NOT NULL,
          breedingBoxId TEXT NOT NULL,
          type TEXT NOT NULL,
          stage TEXT NOT NULL,
          gender TEXT NOT NULL,
          age INTEGER DEFAULT 0,
          health INTEGER DEFAULT 100,
          hunger INTEGER DEFAULT 50,
          happiness INTEGER DEFAULT 75,
          parentIds TEXT DEFAULT '[]',
          bornAt DATETIME DEFAULT CURRENT_TIMESTAMP,
          lastFed DATETIME DEFAULT CURRENT_TIMESTAMP,
          price INTEGER DEFAULT 0,
          FOREIGN KEY (playerId) REFERENCES players (id),
          FOREIGN KEY (breedingBoxId) REFERENCES breeding_boxes (id)
        )
      `);

      console.log('数据库初始化完成');
    } catch (error) {
      console.error('数据库初始化失败:', error);
    }
  }

  // 玩家相关方法
  async createPlayer(player: Omit<Player, 'breedingBoxes' | 'insects'>): Promise<void> {
    await this.run(
      'INSERT INTO players (id, name, money, experience, level, achievements, lastLogin) VALUES (?, ?, ?, ?, ?, ?, ?)',
      [player.id, player.name, player.money, player.experience, player.level, JSON.stringify(player.achievements), player.lastLogin.toISOString()]
    );
  }

  async getPlayer(playerId: string): Promise<Player | null> {
    const row = await this.get('SELECT * FROM players WHERE id = ?', [playerId]) as PlayerRow | undefined;

    if (!row) return null;

    const breedingBoxes = await this.getPlayerBreedingBoxes(playerId);
    const insects = await this.getPlayerInsects(playerId);

    return {
      id: row.id,
      name: row.name,
      money: row.money,
      experience: row.experience,
      level: row.level,
      achievements: JSON.parse(row.achievements),
      lastLogin: new Date(row.lastLogin),
      breedingBoxes: breedingBoxes.map(box => box.id),
      insects: insects.map(insect => insect.id)
    };
  }

  async updatePlayer(player: Player): Promise<void> {
    await this.run(
      'UPDATE players SET name = ?, money = ?, experience = ?, level = ?, achievements = ?, lastLogin = ? WHERE id = ?',
      [player.name, player.money, player.experience, player.level, JSON.stringify(player.achievements), player.lastLogin.toISOString(), player.id]
    );
  }

  // 养殖箱相关方法
  async createBreedingBox(box: BreedingBox, playerId: string): Promise<void> {
    await this.run(
      'INSERT INTO breeding_boxes (id, playerId, name, capacity, currentCount, temperature, humidity, cleanliness, foodLevel, purchasePrice, maintenanceCost) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
      [box.id, playerId, box.name, box.capacity, box.currentCount, box.temperature, box.humidity, box.cleanliness, box.foodLevel, box.purchasePrice, box.maintenanceCost]
    );
  }

  async getPlayerBreedingBoxes(playerId: string): Promise<BreedingBox[]> {
    const rows = await this.all('SELECT * FROM breeding_boxes WHERE playerId = ?', [playerId]) as BreedingBoxRow[];

    const boxes: BreedingBox[] = [];
    for (const row of rows) {
      const insects = await this.getBreedingBoxInsects(row.id);
      boxes.push({
        id: row.id,
        name: row.name,
        capacity: row.capacity,
        currentCount: row.currentCount,
        temperature: row.temperature,
        humidity: row.humidity,
        cleanliness: row.cleanliness,
        foodLevel: row.foodLevel,
        purchasePrice: row.purchasePrice,
        maintenanceCost: row.maintenanceCost,
        insects: insects.map(insect => insect.id)
      });
    }

    return boxes;
  }

  async updateBreedingBox(box: BreedingBox): Promise<void> {
    await this.run(
      'UPDATE breeding_boxes SET name = ?, capacity = ?, currentCount = ?, temperature = ?, humidity = ?, cleanliness = ?, foodLevel = ? WHERE id = ?',
      [box.name, box.capacity, box.currentCount, box.temperature, box.humidity, box.cleanliness, box.foodLevel, box.id]
    );
  }

  // 昆虫相关方法
  async createInsect(insect: Insect, playerId: string): Promise<void> {
    await this.run(
      'INSERT INTO insects (id, playerId, breedingBoxId, type, stage, gender, age, health, hunger, happiness, parentIds, bornAt, lastFed, price) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
      [insect.id, playerId, insect.breedingBoxId, insect.type, insect.stage, insect.gender, insect.age, insect.health, insect.hunger, insect.happiness, JSON.stringify(insect.parentIds || []), insect.bornAt.toISOString(), insect.lastFed.toISOString(), insect.price]
    );
  }

  async getPlayerInsects(playerId: string): Promise<Insect[]> {
    const rows = await this.all('SELECT * FROM insects WHERE playerId = ?', [playerId]) as InsectRow[];

    return rows.map(row => ({
      id: row.id,
      type: row.type as any,
      stage: row.stage as any,
      gender: row.gender as any,
      age: row.age,
      health: row.health,
      hunger: row.hunger,
      happiness: row.happiness,
      breedingBoxId: row.breedingBoxId,
      parentIds: JSON.parse(row.parentIds),
      bornAt: new Date(row.bornAt),
      lastFed: new Date(row.lastFed),
      price: row.price
    }));
  }

  async getBreedingBoxInsects(breedingBoxId: string): Promise<Insect[]> {
    const rows = await this.all('SELECT * FROM insects WHERE breedingBoxId = ?', [breedingBoxId]) as InsectRow[];

    return rows.map(row => ({
      id: row.id,
      type: row.type as any,
      stage: row.stage as any,
      gender: row.gender as any,
      age: row.age,
      health: row.health,
      hunger: row.hunger,
      happiness: row.happiness,
      breedingBoxId: row.breedingBoxId,
      parentIds: JSON.parse(row.parentIds),
      bornAt: new Date(row.bornAt),
      lastFed: new Date(row.lastFed),
      price: row.price
    }));
  }

  async updateInsect(insect: Insect): Promise<void> {
    await this.run(
      'UPDATE insects SET type = ?, stage = ?, gender = ?, age = ?, health = ?, hunger = ?, happiness = ?, breedingBoxId = ?, parentIds = ?, lastFed = ?, price = ? WHERE id = ?',
      [insect.type, insect.stage, insect.gender, insect.age, insect.health, insect.hunger, insect.happiness, insect.breedingBoxId, JSON.stringify(insect.parentIds || []), insect.lastFed.toISOString(), insect.price, insect.id]
    );
  }

  async deleteInsect(insectId: string): Promise<void> {
    await this.run('DELETE FROM insects WHERE id = ?', [insectId]);
  }

  close(): void {
    this.db.close();
  }
}
