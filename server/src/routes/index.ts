import { Express } from 'express';
import { GameManager } from '../game/GameManager';
import { ApiResponse } from '../../../shared/types';

export function setupRoutes(app: Express, gameManager: GameManager): void {
  
  // 健康检查
  app.get('/api/health', (req, res) => {
    res.json({ success: true, message: '服务器运行正常' });
  });

  // 获取游戏统计
  app.get('/api/stats', async (req, res) => {
    try {
      // 这里可以添加全局统计逻辑
      const stats = {
        totalPlayers: 0, // 可以从数据库获取
        totalInsects: 0,
        totalBreedingBoxes: 0
      };
      
      const response: ApiResponse = {
        success: true,
        data: stats
      };
      
      res.json(response);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      const response: ApiResponse = {
        success: false,
        error: errorMessage
      };
      res.status(500).json(response);
    }
  });

  // 获取商店物品
  app.get('/api/shop', (req, res) => {
    const shopItems = [
      {
        id: 'basic-box',
        name: '基础养殖箱',
        description: '容量15只昆虫的基础养殖箱',
        price: 500,
        type: 'breedingBox',
        properties: { capacity: 15, maintenance: 1.0 }
      },
      {
        id: 'advanced-box',
        name: '高级养殖箱',
        description: '容量25只昆虫的高级养殖箱',
        price: 1200,
        type: 'breedingBox',
        properties: { capacity: 25, maintenance: 1.5 }
      },
      {
        id: 'premium-box',
        name: '豪华养殖箱',
        description: '容量40只昆虫的豪华养殖箱',
        price: 2500,
        type: 'breedingBox',
        properties: { capacity: 40, maintenance: 2.0 }
      },
      {
        id: 'food-pack',
        name: '昆虫食物包',
        description: '可以喂食一个养殖箱的所有昆虫',
        price: 10,
        type: 'food',
        properties: { hungerRestore: 30 }
      }
    ];

    const response: ApiResponse = {
      success: true,
      data: shopItems
    };

    res.json(response);
  });

  // 错误处理中间件
  app.use((err: Error, req: any, res: any, next: any) => {
    console.error('API错误:', err);
    const response: ApiResponse = {
      success: false,
      error: '服务器内部错误'
    };
    res.status(500).json(response);
  });
}
