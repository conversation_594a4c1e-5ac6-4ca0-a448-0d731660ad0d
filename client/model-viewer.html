<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FBX模型查看器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #222;
            color: white;
            overflow: hidden;
        }

        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #canvas {
            display: block;
            width: 100%;
            height: 100%;
        }

        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 5px;
            font-size: 14px;
            max-width: 300px;
        }

        #controls {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 5px;
        }

        button {
            background: #4CAF50;
            border: none;
            color: white;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 3px;
            cursor: pointer;
        }

        button:hover {
            background: #45a049;
        }

        #log {
            position: absolute;
            bottom: 10px;
            left: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 10px;
            border-radius: 5px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div id="container">
        <canvas id="canvas"></canvas>
        
        <div id="info">
            <h3>FBX模型查看器</h3>
            <p>使用鼠标拖拽旋转视角</p>
            <p>使用滚轮缩放</p>
            <p>当前模型: <span id="currentModel">无</span></p>
            <p>模型大小: <span id="modelSize">-</span></p>
            <p>多边形数: <span id="polyCount">-</span></p>
        </div>

        <div id="controls">
            <h4>模型控制</h4>
            <button onclick="loadCockroach()">加载蟑螂</button>
            <button onclick="loadBeetle()">加载甲虫</button>
            <button onclick="clearScene()">清空场景</button>
            <br>
            <button onclick="resetCamera()">重置相机</button>
            <button onclick="toggleWireframe()">线框模式</button>
            <button onclick="addTestCube()">测试立方体</button>
        </div>

        <div id="log">
            <div>模型查看器已启动...</div>
        </div>
    </div>

    <script type="module">
        import * as THREE from 'three';
        import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js';
        import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';

        // 全局变量
        let scene, camera, renderer, controls, fbxLoader;
        let currentModel = null;
        let wireframeMode = false;

        // 初始化
        function init() {
            log('初始化Three.js场景...');

            // 创建场景
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x333333);

            // 创建相机
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(0, 2, 5);

            // 创建渲染器
            const canvas = document.getElementById('canvas');
            renderer = new THREE.WebGLRenderer({ canvas, antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;

            // 创建控制器
            controls = new OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;

            // 创建FBX加载器
            fbxLoader = new FBXLoader();

            // 添加光照
            setupLighting();

            // 添加地面
            addGround();

            // 窗口大小调整
            window.addEventListener('resize', onWindowResize);

            log('初始化完成');
            animate();
        }

        function setupLighting() {
            // 环境光
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);

            // 主光源
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(5, 10, 5);
            directionalLight.castShadow = true;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            scene.add(directionalLight);

            // 补充光源
            const fillLight = new THREE.DirectionalLight(0xffffff, 0.3);
            fillLight.position.set(-5, 5, -5);
            scene.add(fillLight);

            log('光照设置完成');
        }

        function addGround() {
            const geometry = new THREE.PlaneGeometry(20, 20);
            const material = new THREE.MeshLambertMaterial({ color: 0x666666 });
            const ground = new THREE.Mesh(geometry, material);
            ground.rotation.x = -Math.PI / 2;
            ground.receiveShadow = true;
            scene.add(ground);
            log('地面添加完成');
        }

        function loadModel(path, name) {
            log(`开始加载模型: ${name} (${path})`);
            
            // 清除当前模型
            if (currentModel) {
                scene.remove(currentModel);
                currentModel = null;
            }

            fbxLoader.load(
                path,
                (fbx) => {
                    log(`模型加载成功: ${name}`);
                    
                    // 获取模型信息
                    const box = new THREE.Box3().setFromObject(fbx);
                    const size = box.getSize(new THREE.Vector3());
                    const center = box.getCenter(new THREE.Vector3());
                    
                    log(`原始大小: ${size.x.toFixed(2)} x ${size.y.toFixed(2)} x ${size.z.toFixed(2)}`);
                    log(`原始中心: ${center.x.toFixed(2)}, ${center.y.toFixed(2)}, ${center.z.toFixed(2)}`);

                    // 居中模型
                    fbx.position.sub(center);
                    
                    // 计算合适的缩放
                    const maxDimension = Math.max(size.x, size.y, size.z);
                    const targetSize = 2; // 目标大小
                    const scale = targetSize / maxDimension;
                    fbx.scale.setScalar(scale);
                    
                    log(`应用缩放: ${scale.toFixed(4)}`);

                    // 设置材质和阴影
                    let polyCount = 0;
                    fbx.traverse((child) => {
                        if (child instanceof THREE.Mesh) {
                            child.castShadow = true;
                            child.receiveShadow = true;
                            
                            if (child.geometry) {
                                polyCount += child.geometry.attributes.position.count / 3;
                            }

                            // 确保材质支持光照
                            if (child.material) {
                                if (Array.isArray(child.material)) {
                                    child.material.forEach(mat => {
                                        if (mat instanceof THREE.MeshBasicMaterial) {
                                            const lambertMat = new THREE.MeshLambertMaterial({
                                                color: mat.color,
                                                map: mat.map
                                            });
                                            child.material = lambertMat;
                                        }
                                    });
                                } else if (child.material instanceof THREE.MeshBasicMaterial) {
                                    const lambertMat = new THREE.MeshLambertMaterial({
                                        color: child.material.color,
                                        map: child.material.map
                                    });
                                    child.material = lambertMat;
                                }
                            }
                        }
                    });

                    // 添加到场景
                    scene.add(fbx);
                    currentModel = fbx;

                    // 更新UI
                    document.getElementById('currentModel').textContent = name;
                    document.getElementById('modelSize').textContent = 
                        `${(size.x * scale).toFixed(2)} x ${(size.y * scale).toFixed(2)} x ${(size.z * scale).toFixed(2)}`;
                    document.getElementById('polyCount').textContent = Math.floor(polyCount);

                    log(`模型添加到场景完成，多边形数: ${Math.floor(polyCount)}`);
                },
                (progress) => {
                    const percent = (progress.loaded / progress.total * 100).toFixed(1);
                    log(`加载进度: ${percent}%`);
                },
                (error) => {
                    log(`模型加载失败: ${error.message}`);
                    console.error('FBX加载错误:', error);
                }
            );
        }

        function animate() {
            requestAnimationFrame(animate);
            controls.update();
            renderer.render(scene, camera);
        }

        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }

        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<div>[${time}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        // 全局函数
        window.loadCockroach = () => loadModel('/models/cockroach.fbx', '蟑螂');
        window.loadBeetle = () => loadModel('/models/beetle.fbx', '甲虫');
        
        window.clearScene = () => {
            if (currentModel) {
                scene.remove(currentModel);
                currentModel = null;
                document.getElementById('currentModel').textContent = '无';
                document.getElementById('modelSize').textContent = '-';
                document.getElementById('polyCount').textContent = '-';
                log('场景已清空');
            }
        };

        window.resetCamera = () => {
            camera.position.set(0, 2, 5);
            controls.reset();
            log('相机已重置');
        };

        window.toggleWireframe = () => {
            wireframeMode = !wireframeMode;
            if (currentModel) {
                currentModel.traverse((child) => {
                    if (child instanceof THREE.Mesh && child.material) {
                        if (Array.isArray(child.material)) {
                            child.material.forEach(mat => mat.wireframe = wireframeMode);
                        } else {
                            child.material.wireframe = wireframeMode;
                        }
                    }
                });
            }
            log(`线框模式: ${wireframeMode ? '开启' : '关闭'}`);
        };

        window.addTestCube = () => {
            const geometry = new THREE.BoxGeometry(1, 1, 1);
            const material = new THREE.MeshLambertMaterial({ color: 0xff0000 });
            const cube = new THREE.Mesh(geometry, material);
            cube.position.set(3, 0.5, 0);
            cube.castShadow = true;
            scene.add(cube);
            log('测试立方体已添加');
        };

        // 启动
        init();
    </script>
</body>
</html>
