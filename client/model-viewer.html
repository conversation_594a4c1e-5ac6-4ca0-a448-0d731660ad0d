<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FBX模型查看器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #222;
            color: white;
            overflow: hidden;
        }

        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #canvas {
            display: block;
            width: 100%;
            height: 100%;
        }

        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 5px;
            font-size: 14px;
            max-width: 300px;
        }

        #controls {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 5px;
            max-width: 250px;
        }

        #animation-controls {
            position: absolute;
            top: 200px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 5px;
            max-width: 250px;
        }

        #stage-controls {
            position: absolute;
            top: 350px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 5px;
            max-width: 250px;
        }

        #ai-controls {
            position: absolute;
            top: 520px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 5px;
            max-width: 250px;
        }

        #environment-controls {
            position: absolute;
            top: 10px;
            left: 320px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 5px;
            max-width: 250px;
        }

        button {
            background: #4CAF50;
            border: none;
            color: white;
            padding: 8px 16px;
            margin: 2px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }

        button:hover {
            background: #45a049;
        }

        button.active {
            background: #FF6B35;
        }

        button.danger {
            background: #f44336;
        }

        button.danger:hover {
            background: #da190b;
        }

        .slider-container {
            margin: 8px 0;
        }

        .slider-container label {
            display: block;
            font-size: 12px;
            margin-bottom: 3px;
        }

        input[type="range"] {
            width: 100%;
            margin: 5px 0;
        }

        .value-display {
            font-size: 11px;
            color: #ccc;
        }

        h4 {
            margin: 0 0 10px 0;
            color: #fff;
            border-bottom: 1px solid #555;
            padding-bottom: 5px;
        }

        .stage-btn {
            width: 48%;
            margin: 1%;
        }

        .stage-btn.active {
            background: #FF6B35;
        }

        #info p {
            margin: 5px 0;
            font-size: 13px;
        }

        #log div {
            margin: 2px 0;
            padding: 2px 5px;
            border-left: 3px solid #4CAF50;
            background: rgba(76, 175, 80, 0.1);
        }

        #log div:nth-child(even) {
            background: rgba(255, 255, 255, 0.05);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            #controls, #animation-controls, #stage-controls {
                position: relative;
                top: auto;
                right: auto;
                margin: 10px;
                max-width: none;
            }

            #info {
                position: relative;
                top: auto;
                left: auto;
                margin: 10px;
                max-width: none;
            }

            #log {
                position: relative;
                bottom: auto;
                left: auto;
                right: auto;
                margin: 10px;
            }
        }

        #log {
            position: absolute;
            bottom: 10px;
            left: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 10px;
            border-radius: 5px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div id="container">
        <canvas id="canvas"></canvas>
        
        <div id="info">
            <h3>FBX模型查看器</h3>
            <p>使用鼠标拖拽旋转视角</p>
            <p>使用滚轮缩放</p>
            <p>当前模型: <span id="currentModel">无</span></p>
            <p>模型大小: <span id="modelSize">-</span></p>
            <p>多边形数: <span id="polyCount">-</span></p>
        </div>

        <div id="controls">
            <h4>模型控制</h4>
            <button onclick="loadCockroach()">加载蟑螂</button>
            <button onclick="loadBeetle()">加载甲虫</button>
            <button onclick="clearScene()" class="danger">清空场景</button>
            <br>
            <button onclick="resetCamera()">重置相机</button>
            <button onclick="toggleWireframe()" id="wireframeBtn">线框模式</button>
            <button onclick="addTestCube()">测试立方体</button>
            <br>
            <button onclick="addMultipleInsects()">添加多只昆虫</button>
            <button onclick="exportModel()">导出模型信息</button>
        </div>

        <div id="animation-controls">
            <h4>动画控制</h4>
            <button onclick="toggleAnimation()" id="animationBtn">开始动画</button>
            <button onclick="resetAnimation()">重置动画</button>
            <br>

            <div class="slider-container">
                <label>动画速度: <span id="speedValue">1.0</span></label>
                <input type="range" id="speedSlider" min="0" max="3" step="0.1" value="1" onchange="updateAnimationSpeed()">
            </div>

            <div class="slider-container">
                <label>旋转速度: <span id="rotationValue">1.0</span></label>
                <input type="range" id="rotationSlider" min="0" max="5" step="0.1" value="1" onchange="updateRotationSpeed()">
            </div>

            <div class="slider-container">
                <label>跳跃高度: <span id="jumpValue">0.1</span></label>
                <input type="range" id="jumpSlider" min="0" max="1" step="0.05" value="0.1" onchange="updateJumpHeight()">
            </div>
        </div>

        <div id="stage-controls">
            <h4>生命阶段模拟</h4>
            <button onclick="setStage('egg')" class="stage-btn" data-stage="egg">卵</button>
            <button onclick="setStage('larva')" class="stage-btn" data-stage="larva">幼虫</button>
            <button onclick="setStage('pupa')" class="stage-btn" data-stage="pupa">蛹</button>
            <button onclick="setStage('adult')" class="stage-btn" data-stage="adult">成虫</button>
            <br>

            <div class="slider-container">
                <label>健康度: <span id="healthValue">100</span></label>
                <input type="range" id="healthSlider" min="0" max="100" step="5" value="100" onchange="updateHealth()">
            </div>

            <div class="slider-container">
                <label>饥饿度: <span id="hungerValue">50</span></label>
                <input type="range" id="hungerSlider" min="0" max="100" step="5" value="50" onchange="updateHunger()">
            </div>

            <div class="slider-container">
                <label>快乐度: <span id="happinessValue">75</span></label>
                <input type="range" id="happinessSlider" min="0" max="100" step="5" value="75" onchange="updateHappiness()">
            </div>
        </div>

        <div id="ai-controls">
            <h4>🧠 AI行为控制</h4>
            <button onclick="toggleAI()" id="aiBtn">启动AI</button>
            <button onclick="resetAI()">重置AI</button>
            <br>

            <div class="slider-container">
                <label>觅食欲望: <span id="foragingValue">70</span></label>
                <input type="range" id="foragingSlider" min="0" max="100" step="5" value="70" onchange="updateForaging()">
            </div>

            <div class="slider-container">
                <label>社交性: <span id="socialValue">50</span></label>
                <input type="range" id="socialSlider" min="0" max="100" step="5" value="50" onchange="updateSocial()">
            </div>

            <div class="slider-container">
                <label>探索欲: <span id="explorationValue">60</span></label>
                <input type="range" id="explorationSlider" min="0" max="100" step="5" value="60" onchange="updateExploration()">
            </div>

            <button onclick="addPheromoneTrail()">添加信息素轨迹</button>
            <button onclick="clearPheromones()">清除信息素</button>
        </div>

        <div id="environment-controls">
            <h4>🌍 环境控制</h4>
            <button onclick="addFoodSources()">添加食物源</button>
            <button onclick="addObstacles()">添加障碍物</button>
            <button onclick="addNest()">添加巢穴</button>
            <button onclick="clearEnvironment()" class="danger">清空环境</button>
            <br>

            <div class="slider-container">
                <label>食物数量: <span id="foodCountValue">5</span></label>
                <input type="range" id="foodCountSlider" min="1" max="20" step="1" value="5" onchange="updateFoodCount()">
            </div>

            <div class="slider-container">
                <label>环境大小: <span id="environmentSizeValue">10</span></label>
                <input type="range" id="environmentSizeSlider" min="5" max="20" step="1" value="10" onchange="updateEnvironmentSize()">
            </div>

            <p style="font-size: 11px; color: #ccc;">
                🟢 食物 | 🔴 障碍物 | 🏠 巢穴 | 💨 信息素
            </p>
        </div>

        <div id="log">
            <div>模型查看器已启动...</div>
        </div>
    </div>

    <script type="module">
        import * as THREE from 'three';
        import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js';
        import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';

        // 全局变量
        let scene, camera, renderer, controls, fbxLoader;
        let currentModel = null;
        let wireframeMode = false;
        let animationEnabled = false;
        let animationSpeed = 1.0;
        let rotationSpeed = 1.0;
        let jumpHeight = 0.1;
        let currentStage = 'adult';
        let insectStats = { health: 100, hunger: 50, happiness: 75 };
        let multipleInsects = [];
        let animationStartTime = 0;

        // AI系统变量
        let aiEnabled = false;
        let aiSettings = {
            foraging: 70,
            social: 50,
            exploration: 60
        };
        let environment = {
            foodSources: [],
            obstacles: [],
            nest: null,
            pheromoneTrails: [],
            size: 10
        };
        let aiUpdateInterval = null;

        // 初始化
        function init() {
            log('初始化Three.js场景...');

            // 创建场景
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x333333);

            // 创建相机
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(0, 2, 5);

            // 创建渲染器
            const canvas = document.getElementById('canvas');
            renderer = new THREE.WebGLRenderer({ canvas, antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;

            // 创建控制器
            controls = new OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;

            // 创建FBX加载器
            fbxLoader = new FBXLoader();

            // 添加光照
            setupLighting();

            // 添加地面
            addGround();

            // 窗口大小调整
            window.addEventListener('resize', onWindowResize);

            log('初始化完成');
            animate();
        }

        function setupLighting() {
            // 环境光
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);

            // 主光源
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(5, 10, 5);
            directionalLight.castShadow = true;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            scene.add(directionalLight);

            // 补充光源
            const fillLight = new THREE.DirectionalLight(0xffffff, 0.3);
            fillLight.position.set(-5, 5, -5);
            scene.add(fillLight);

            log('光照设置完成');
        }

        function addGround() {
            const geometry = new THREE.PlaneGeometry(20, 20);
            const material = new THREE.MeshLambertMaterial({ color: 0x666666 });
            const ground = new THREE.Mesh(geometry, material);
            ground.rotation.x = -Math.PI / 2;
            ground.receiveShadow = true;
            scene.add(ground);
            log('地面添加完成');
        }

        function loadModel(path, name) {
            log(`开始加载模型: ${name} (${path})`);
            
            // 清除当前模型
            if (currentModel) {
                scene.remove(currentModel);
                currentModel = null;
            }

            fbxLoader.load(
                path,
                (fbx) => {
                    log(`模型加载成功: ${name}`);
                    
                    // 获取模型信息
                    const box = new THREE.Box3().setFromObject(fbx);
                    const size = box.getSize(new THREE.Vector3());
                    const center = box.getCenter(new THREE.Vector3());
                    
                    log(`原始大小: ${size.x.toFixed(2)} x ${size.y.toFixed(2)} x ${size.z.toFixed(2)}`);
                    log(`原始中心: ${center.x.toFixed(2)}, ${center.y.toFixed(2)}, ${center.z.toFixed(2)}`);

                    // 居中模型
                    fbx.position.sub(center);
                    
                    // 计算合适的缩放
                    const maxDimension = Math.max(size.x, size.y, size.z);
                    const targetSize = 2; // 目标大小
                    const scale = targetSize / maxDimension;
                    fbx.scale.setScalar(scale);
                    
                    log(`应用缩放: ${scale.toFixed(4)}`);

                    // 设置材质和阴影
                    let polyCount = 0;
                    fbx.traverse((child) => {
                        if (child instanceof THREE.Mesh) {
                            child.castShadow = true;
                            child.receiveShadow = true;
                            
                            if (child.geometry) {
                                polyCount += child.geometry.attributes.position.count / 3;
                            }

                            // 确保材质支持光照
                            if (child.material) {
                                if (Array.isArray(child.material)) {
                                    child.material.forEach(mat => {
                                        if (mat instanceof THREE.MeshBasicMaterial) {
                                            const lambertMat = new THREE.MeshLambertMaterial({
                                                color: mat.color,
                                                map: mat.map
                                            });
                                            child.material = lambertMat;
                                        }
                                    });
                                } else if (child.material instanceof THREE.MeshBasicMaterial) {
                                    const lambertMat = new THREE.MeshLambertMaterial({
                                        color: child.material.color,
                                        map: child.material.map
                                    });
                                    child.material = lambertMat;
                                }
                            }
                        }
                    });

                    // 添加到场景
                    scene.add(fbx);
                    currentModel = fbx;

                    // 更新UI
                    document.getElementById('currentModel').textContent = name;
                    document.getElementById('modelSize').textContent = 
                        `${(size.x * scale).toFixed(2)} x ${(size.y * scale).toFixed(2)} x ${(size.z * scale).toFixed(2)}`;
                    document.getElementById('polyCount').textContent = Math.floor(polyCount);

                    log(`模型添加到场景完成，多边形数: ${Math.floor(polyCount)}`);
                },
                (progress) => {
                    const percent = (progress.loaded / progress.total * 100).toFixed(1);
                    log(`加载进度: ${percent}%`);
                },
                (error) => {
                    log(`模型加载失败: ${error.message}`);
                    console.error('FBX加载错误:', error);
                }
            );
        }

        function animate() {
            requestAnimationFrame(animate);
            controls.update();

            // 更新动画
            if (animationEnabled) {
                updateModelAnimations();
            }

            renderer.render(scene, camera);
        }

        function updateModelAnimations() {
            const time = (Date.now() - animationStartTime) * 0.001 * animationSpeed;

            if (aiEnabled) {
                // AI控制的动画
                updateAIBehavior(time);
            } else {
                // 传统动画
                if (currentModel) {
                    updateSingleModelAnimation(currentModel, time, 0);
                }

                multipleInsects.forEach((insect, index) => {
                    updateSingleModelAnimation(insect.model, time, index);
                });
            }
        }

        function updateSingleModelAnimation(model, time, index) {
            if (!model) return;

            const offset = index * 0.5; // 为每个昆虫创建不同的动画偏移

            // 基础位置动画
            const baseY = 0;

            switch (currentStage) {
                case 'egg':
                    // 卵的呼吸效果
                    const breathScale = 1 + Math.sin(time * 2 + offset) * 0.02;
                    model.scale.setScalar(0.5 * breathScale);
                    break;

                case 'larva':
                    // 幼虫的蠕动
                    model.position.y = baseY + Math.sin(time * 3 + offset) * jumpHeight;
                    model.rotation.z = Math.sin(time * 2 + offset) * 0.1;
                    break;

                case 'pupa':
                    // 蛹的轻微摆动
                    model.rotation.x = Math.sin(time * 1.5 + offset) * 0.05;
                    model.rotation.z = Math.cos(time * 1.2 + offset) * 0.03;
                    break;

                case 'adult':
                    // 成虫的复杂动画
                    if (insectStats.health > 50 && insectStats.hunger > 30) {
                        // 健康的成虫
                        model.position.y = baseY + Math.sin(time * 4 + offset) * jumpHeight;
                        model.rotation.y += Math.sin(time * 0.5 + offset) * 0.02 * rotationSpeed;

                        // 偶尔改变朝向
                        if (Math.sin(time * 0.1 + offset) > 0.95) {
                            model.rotation.y += (Math.random() - 0.5) * 0.5;
                        }
                    } else {
                        // 不健康的成虫
                        model.position.y = baseY + Math.sin(time * 1 + offset) * jumpHeight * 0.5;
                    }
                    break;
            }

            // 根据快乐度调整动画
            const happinessMultiplier = insectStats.happiness / 100;
            if (happinessMultiplier < 0.5) {
                model.position.y *= 0.5;
                if (model.rotation.y !== undefined) {
                    model.rotation.y *= 0.5;
                }
            }
        }

        // ===== AI行为系统 =====

        function updateAIBehavior(time) {
            multipleInsects.forEach((insect, index) => {
                if (!insect.ai) {
                    initializeInsectAI(insect, index);
                }

                updateInsectAI(insect, time);
                applyAIMovement(insect, time);
            });

            // 更新信息素轨迹
            updatePheromoneTrails(time);

            // 更新环境动画
            updateEnvironmentAnimations(time);
        }

        function initializeInsectAI(insect, index) {
            insect.ai = {
                state: 'exploring', // exploring, foraging, returning, following, resting
                target: null,
                path: [],
                energy: 100,
                carryingFood: false,
                lastStateChange: Date.now(),
                personalityTraits: {
                    curiosity: Math.random() * 0.5 + 0.5, // 0.5-1.0
                    boldness: Math.random() * 0.5 + 0.5,
                    efficiency: Math.random() * 0.5 + 0.5
                },
                memory: {
                    knownFoodSources: [],
                    visitedLocations: [],
                    pheromoneStrength: 0
                },
                lastPosition: insect.model.position.clone(),
                speed: 0.5 + Math.random() * 0.5, // 0.5-1.0
                detectionRange: 2 + Math.random() * 2 // 2-4
            };
        }

        function updateInsectAI(insect, time) {
            const ai = insect.ai;
            const position = insect.model.position;
            const timeSinceStateChange = Date.now() - ai.lastStateChange;

            // 能量消耗
            ai.energy -= 0.1;
            if (ai.energy < 0) ai.energy = 0;

            // 状态机逻辑
            switch (ai.state) {
                case 'exploring':
                    handleExploringState(insect, time);
                    break;
                case 'foraging':
                    handleForagingState(insect, time);
                    break;
                case 'returning':
                    handleReturningState(insect, time);
                    break;
                case 'following':
                    handleFollowingState(insect, time);
                    break;
                case 'resting':
                    handleRestingState(insect, time);
                    break;
            }

            // 随机状态切换（模拟真实蚂蚁的不确定性）
            if (timeSinceStateChange > 5000 && Math.random() < 0.1) {
                if (ai.energy < 30) {
                    changeInsectState(insect, 'resting');
                } else if (Math.random() < aiSettings.exploration / 100) {
                    changeInsectState(insect, 'exploring');
                }
            }
        }

        function handleExploringState(insect, time) {
            const ai = insect.ai;

            // 寻找食物
            const nearbyFood = findNearbyFood(insect.model.position, ai.detectionRange);
            if (nearbyFood && Math.random() < aiSettings.foraging / 100) {
                ai.target = nearbyFood;
                changeInsectState(insect, 'foraging');
                return;
            }

            // 跟随信息素轨迹
            const nearbyPheromone = findNearbyPheromone(insect.model.position, ai.detectionRange);
            if (nearbyPheromone && Math.random() < aiSettings.social / 100) {
                ai.target = nearbyPheromone;
                changeInsectState(insect, 'following');
                return;
            }

            // 随机探索
            if (!ai.target || getDistance(insect.model.position, ai.target) < 0.5) {
                ai.target = generateRandomTarget(insect.model.position, ai.personalityTraits.curiosity);
            }
        }

        function handleForagingState(insect, time) {
            const ai = insect.ai;

            if (!ai.target) {
                changeInsectState(insect, 'exploring');
                return;
            }

            // 到达食物源
            if (getDistance(insect.model.position, ai.target) < 0.3) {
                ai.carryingFood = true;
                ai.energy = Math.min(100, ai.energy + 20);

                // 记住这个食物源
                if (!ai.memory.knownFoodSources.includes(ai.target)) {
                    ai.memory.knownFoodSources.push(ai.target);
                }

                // 留下信息素轨迹
                addPheromoneAt(insect.model.position, 'food');

                // 返回巢穴
                if (environment.nest) {
                    ai.target = environment.nest.position;
                    changeInsectState(insect, 'returning');
                } else {
                    changeInsectState(insect, 'exploring');
                }
            }
        }

        function handleReturningState(insect, time) {
            const ai = insect.ai;

            if (!ai.target) {
                changeInsectState(insect, 'exploring');
                return;
            }

            // 到达巢穴
            if (getDistance(insect.model.position, ai.target) < 0.5) {
                ai.carryingFood = false;
                ai.energy = 100;

                // 留下更强的信息素轨迹
                addPheromoneAt(insect.model.position, 'nest');

                // 决定下一步行动
                if (ai.memory.knownFoodSources.length > 0 && Math.random() < 0.7) {
                    ai.target = ai.memory.knownFoodSources[Math.floor(Math.random() * ai.memory.knownFoodSources.length)];
                    changeInsectState(insect, 'foraging');
                } else {
                    changeInsectState(insect, 'exploring');
                }
            }
        }

        function handleFollowingState(insect, time) {
            const ai = insect.ai;

            // 跟随信息素轨迹
            const nextPheromone = findStrongestPheromoneDirection(insect.model.position, ai.detectionRange);
            if (nextPheromone) {
                ai.target = nextPheromone;
            } else {
                changeInsectState(insect, 'exploring');
            }
        }

        function handleRestingState(insect, time) {
            const ai = insect.ai;

            // 恢复能量
            ai.energy += 0.5;
            if (ai.energy >= 100) {
                ai.energy = 100;
                changeInsectState(insect, 'exploring');
            }

            // 休息时基本不移动
            ai.target = insect.model.position;
        }

        function changeInsectState(insect, newState) {
            insect.ai.state = newState;
            insect.ai.lastStateChange = Date.now();
            log(`昆虫 ${insect.id} 状态变更: ${newState}`);
        }

        function applyAIMovement(insect, time) {
            const ai = insect.ai;
            const position = insect.model.position;

            if (!ai.target) return;

            // 计算移动方向
            const direction = new THREE.Vector3()
                .subVectors(ai.target, position)
                .normalize();

            // 应用移动速度
            let moveSpeed = ai.speed * 0.02; // 基础移动速度

            // 根据状态调整速度
            switch (ai.state) {
                case 'foraging':
                    moveSpeed *= 1.5; // 觅食时更快
                    break;
                case 'returning':
                    moveSpeed *= ai.carryingFood ? 0.8 : 1.2; // 携带食物时较慢
                    break;
                case 'resting':
                    moveSpeed *= 0.1; // 休息时几乎不动
                    break;
                case 'following':
                    moveSpeed *= 1.3; // 跟随信息素时较快
                    break;
            }

            // 根据能量调整速度
            moveSpeed *= (ai.energy / 100);

            // 应用移动
            position.add(direction.multiplyScalar(moveSpeed));

            // 边界检查
            const boundary = environment.size;
            position.x = Math.max(-boundary, Math.min(boundary, position.x));
            position.z = Math.max(-boundary, Math.min(boundary, position.z));

            // 面向移动方向
            if (direction.length() > 0.01) {
                const targetRotation = Math.atan2(direction.x, direction.z);
                insect.model.rotation.y = targetRotation;
            }

            // 根据状态添加视觉效果
            updateInsectVisualEffects(insect, time);
        }

        function updateInsectVisualEffects(insect, time) {
            const ai = insect.ai;
            const model = insect.model;

            // 根据状态调整颜色
            model.traverse((child) => {
                if (child instanceof THREE.Mesh && child.material) {
                    const material = child.material;

                    // 重置颜色
                    material.color.setHex(insect.type === 'cockroach' ? 0x8B4513 : 0x2F4F2F);

                    // 根据状态着色
                    switch (ai.state) {
                        case 'foraging':
                            material.color.multiplyScalar(1.2); // 稍微发亮
                            break;
                        case 'returning':
                            if (ai.carryingFood) {
                                material.color.setHex(0xFFD700); // 金色表示携带食物
                            }
                            break;
                        case 'resting':
                            material.color.multiplyScalar(0.6); // 变暗
                            break;
                        case 'following':
                            material.color.setHex(0x00FF00); // 绿色表示跟随信息素
                            break;
                    }

                    // 根据能量调整透明度
                    material.transparent = true;
                    material.opacity = 0.5 + (ai.energy / 100) * 0.5;
                }
            });

            // 添加状态指示器
            addStateIndicator(insect, time);
        }

        function addStateIndicator(insect, time) {
            // 移除旧的指示器
            const oldIndicator = insect.model.children.find(child => child.userData.isIndicator);
            if (oldIndicator) {
                insect.model.remove(oldIndicator);
            }

            // 创建新的指示器
            let indicatorGeometry, indicatorMaterial;
            const ai = insect.ai;

            switch (ai.state) {
                case 'foraging':
                    indicatorGeometry = new THREE.SphereGeometry(0.1);
                    indicatorMaterial = new THREE.MeshBasicMaterial({ color: 0x00FF00 });
                    break;
                case 'returning':
                    indicatorGeometry = new THREE.ConeGeometry(0.1, 0.2);
                    indicatorMaterial = new THREE.MeshBasicMaterial({ color: ai.carryingFood ? 0xFFD700 : 0x0000FF });
                    break;
                case 'resting':
                    indicatorGeometry = new THREE.RingGeometry(0.05, 0.15);
                    indicatorMaterial = new THREE.MeshBasicMaterial({ color: 0x888888 });
                    break;
                case 'following':
                    indicatorGeometry = new THREE.BoxGeometry(0.1, 0.1, 0.1);
                    indicatorMaterial = new THREE.MeshBasicMaterial({ color: 0xFF00FF });
                    break;
                default:
                    return; // 探索状态不显示指示器
            }

            const indicator = new THREE.Mesh(indicatorGeometry, indicatorMaterial);
            indicator.position.set(0, 0.5, 0);
            indicator.userData.isIndicator = true;

            // 添加轻微的浮动动画
            indicator.position.y += Math.sin(time * 3) * 0.05;

            insect.model.add(indicator);
        }

        // ===== 环境交互函数 =====

        function findNearbyFood(position, range) {
            return environment.foodSources.find(food =>
                getDistance(position, food.position) <= range
            );
        }

        function findNearbyPheromone(position, range) {
            const nearby = environment.pheromoneTrails.filter(pheromone =>
                getDistance(position, pheromone.position) <= range && pheromone.strength > 0.1
            );

            if (nearby.length === 0) return null;

            // 返回最强的信息素
            return nearby.reduce((strongest, current) =>
                current.strength > strongest.strength ? current : strongest
            );
        }

        function findStrongestPheromoneDirection(position, range) {
            const nearby = environment.pheromoneTrails.filter(pheromone =>
                getDistance(position, pheromone.position) <= range && pheromone.strength > 0.1
            );

            if (nearby.length === 0) return null;

            // 找到最强的信息素方向
            const strongest = nearby.reduce((max, current) =>
                current.strength > max.strength ? current : max
            );

            return strongest.position;
        }

        function generateRandomTarget(currentPosition, curiosityFactor) {
            const angle = Math.random() * Math.PI * 2;
            const distance = (1 + Math.random() * 3) * curiosityFactor;

            return new THREE.Vector3(
                currentPosition.x + Math.cos(angle) * distance,
                0,
                currentPosition.z + Math.sin(angle) * distance
            );
        }

        function getDistance(pos1, pos2) {
            return pos1.distanceTo(pos2);
        }

        function addPheromoneAt(position, type) {
            const pheromone = {
                position: position.clone(),
                type: type, // 'food', 'nest', 'danger'
                strength: 1.0,
                createdAt: Date.now(),
                maxAge: 30000 // 30秒后消失
            };

            environment.pheromoneTrails.push(pheromone);

            // 创建视觉表示
            createPheromoneVisual(pheromone);
        }

        function createPheromoneVisual(pheromone) {
            const geometry = new THREE.SphereGeometry(0.05);
            let color;

            switch (pheromone.type) {
                case 'food':
                    color = 0x00FF00; // 绿色
                    break;
                case 'nest':
                    color = 0x0000FF; // 蓝色
                    break;
                case 'danger':
                    color = 0xFF0000; // 红色
                    break;
                default:
                    color = 0xFFFFFF; // 白色
            }

            const material = new THREE.MeshBasicMaterial({
                color: color,
                transparent: true,
                opacity: 0.6
            });

            const mesh = new THREE.Mesh(geometry, material);
            mesh.position.copy(pheromone.position);
            mesh.position.y = 0.1;
            mesh.userData.isPheromone = true;
            mesh.userData.pheromone = pheromone;

            scene.add(mesh);
            pheromone.visual = mesh;
        }

        function updatePheromoneTrails(time) {
            const currentTime = Date.now();

            // 更新信息素强度和移除过期的
            environment.pheromoneTrails = environment.pheromoneTrails.filter(pheromone => {
                const age = currentTime - pheromone.createdAt;
                const ageRatio = age / pheromone.maxAge;

                if (ageRatio >= 1) {
                    // 移除过期的信息素
                    if (pheromone.visual) {
                        scene.remove(pheromone.visual);
                    }
                    return false;
                }

                // 更新强度（随时间衰减）
                pheromone.strength = 1 - ageRatio;

                // 更新视觉效果
                if (pheromone.visual) {
                    pheromone.visual.material.opacity = pheromone.strength * 0.6;
                    pheromone.visual.scale.setScalar(pheromone.strength);
                }

                return true;
            });
        }

        function updateEnvironmentAnimations(time) {
            // 食物浮动动画
            environment.foodSources.forEach(food => {
                if (food.visual && food.visual.userData.originalY !== undefined) {
                    const floatOffset = food.visual.userData.floatOffset || 0;
                    food.visual.position.y = food.visual.userData.originalY +
                        Math.sin(time * 2 + floatOffset) * 0.05;
                }
            });

            // 巢穴轻微脉动
            if (environment.nest && environment.nest.visual) {
                const scale = 1 + Math.sin(time * 1.5) * 0.02;
                environment.nest.visual.scale.setScalar(scale);
            }
        }

        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }

        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<div>[${time}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        // 模型加载函数
        window.loadCockroach = () => loadModel('/models/cockroach.fbx', '蟑螂');
        window.loadBeetle = () => loadModel('/models/beetle.fbx', '甲虫');
        
        window.clearScene = () => {
            if (currentModel) {
                scene.remove(currentModel);
                currentModel = null;
                document.getElementById('currentModel').textContent = '无';
                document.getElementById('modelSize').textContent = '-';
                document.getElementById('polyCount').textContent = '-';
                log('场景已清空');
            }
        };

        window.resetCamera = () => {
            camera.position.set(0, 2, 5);
            controls.reset();
            log('相机已重置');
        };

        window.toggleWireframe = () => {
            wireframeMode = !wireframeMode;
            const btn = document.getElementById('wireframeBtn');

            function applyWireframe(model) {
                if (model) {
                    model.traverse((child) => {
                        if (child instanceof THREE.Mesh && child.material) {
                            if (Array.isArray(child.material)) {
                                child.material.forEach(mat => mat.wireframe = wireframeMode);
                            } else {
                                child.material.wireframe = wireframeMode;
                            }
                        }
                    });
                }
            }

            // 应用到当前模型
            applyWireframe(currentModel);

            // 应用到多个昆虫
            multipleInsects.forEach(insect => applyWireframe(insect.model));

            btn.textContent = wireframeMode ? '实体模式' : '线框模式';
            btn.classList.toggle('active', wireframeMode);
            log(`线框模式: ${wireframeMode ? '开启' : '关闭'}`);
        };

        window.addTestCube = () => {
            const geometry = new THREE.BoxGeometry(1, 1, 1);
            const material = new THREE.MeshLambertMaterial({ color: 0xff0000 });
            const cube = new THREE.Mesh(geometry, material);
            cube.position.set(3, 0.5, 0);
            cube.castShadow = true;
            scene.add(cube);
            log('测试立方体已添加');
        };

        // 动画控制函数
        window.toggleAnimation = () => {
            animationEnabled = !animationEnabled;
            const btn = document.getElementById('animationBtn');

            if (animationEnabled) {
                animationStartTime = Date.now();
                btn.textContent = '停止动画';
                btn.classList.add('active');
                log('动画已开始');
            } else {
                btn.textContent = '开始动画';
                btn.classList.remove('active');
                log('动画已停止');
            }
        };

        window.resetAnimation = () => {
            animationStartTime = Date.now();

            // 重置当前模型位置
            if (currentModel) {
                currentModel.position.set(0, 0, 0);
                currentModel.rotation.set(0, 0, 0);
                currentModel.scale.setScalar(1);
            }

            // 重置多个昆虫位置
            multipleInsects.forEach((insect, index) => {
                const angle = (index / multipleInsects.length) * Math.PI * 2;
                insect.model.position.set(
                    Math.cos(angle) * 2,
                    0,
                    Math.sin(angle) * 2
                );
                insect.model.rotation.set(0, 0, 0);
                insect.model.scale.setScalar(1);
            });

            log('动画已重置');
        };

        // 滑块控制函数
        window.updateAnimationSpeed = () => {
            const slider = document.getElementById('speedSlider');
            const display = document.getElementById('speedValue');
            animationSpeed = parseFloat(slider.value);
            display.textContent = animationSpeed.toFixed(1);
            log(`动画速度设置为: ${animationSpeed}`);
        };

        window.updateRotationSpeed = () => {
            const slider = document.getElementById('rotationSlider');
            const display = document.getElementById('rotationValue');
            rotationSpeed = parseFloat(slider.value);
            display.textContent = rotationSpeed.toFixed(1);
            log(`旋转速度设置为: ${rotationSpeed}`);
        };

        window.updateJumpHeight = () => {
            const slider = document.getElementById('jumpSlider');
            const display = document.getElementById('jumpValue');
            jumpHeight = parseFloat(slider.value);
            display.textContent = jumpHeight.toFixed(2);
            log(`跳跃高度设置为: ${jumpHeight}`);
        };

        // 生命阶段控制
        window.setStage = (stage) => {
            currentStage = stage;

            // 更新按钮状态
            document.querySelectorAll('.stage-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-stage="${stage}"]`).classList.add('active');

            // 应用阶段效果到当前模型
            applyStageEffects(currentModel, stage);

            // 应用到多个昆虫
            multipleInsects.forEach(insect => {
                applyStageEffects(insect.model, stage);
            });

            log(`生命阶段设置为: ${stage}`);
        };

        function applyStageEffects(model, stage) {
            if (!model) return;

            let scale = 1;

            switch (stage) {
                case 'egg':
                    scale = 0.5;
                    model.traverse((child) => {
                        if (child instanceof THREE.Mesh && child.material) {
                            child.material.color.setHex(0xFFFACD); // 奶白色
                            child.material.transparent = true;
                            child.material.opacity = 0.9;
                        }
                    });
                    break;
                case 'larva':
                    scale = 0.7;
                    model.traverse((child) => {
                        if (child instanceof THREE.Mesh && child.material) {
                            child.material.color.multiplyScalar(1.2);
                        }
                    });
                    break;
                case 'pupa':
                    scale = 0.9;
                    model.traverse((child) => {
                        if (child instanceof THREE.Mesh && child.material) {
                            child.material.color.setHex(0x8B4513); // 棕色
                            child.material.transparent = true;
                            child.material.opacity = 0.95;
                        }
                    });
                    break;
                case 'adult':
                    scale = 1.0;
                    // 恢复原始颜色需要重新加载模型
                    break;
            }

            model.scale.setScalar(scale);
        }

        // 状态控制函数
        window.updateHealth = () => {
            const slider = document.getElementById('healthSlider');
            const display = document.getElementById('healthValue');
            insectStats.health = parseInt(slider.value);
            display.textContent = insectStats.health;

            // 应用健康效果
            applyHealthEffects();
            log(`健康度设置为: ${insectStats.health}`);
        };

        window.updateHunger = () => {
            const slider = document.getElementById('hungerSlider');
            const display = document.getElementById('hungerValue');
            insectStats.hunger = parseInt(slider.value);
            display.textContent = insectStats.hunger;
            log(`饥饿度设置为: ${insectStats.hunger}`);
        };

        window.updateHappiness = () => {
            const slider = document.getElementById('happinessSlider');
            const display = document.getElementById('happinessValue');
            insectStats.happiness = parseInt(slider.value);
            display.textContent = insectStats.happiness;
            log(`快乐度设置为: ${insectStats.happiness}`);
        };

        function applyHealthEffects() {
            const colorMultiplier = insectStats.health < 50 ? 0.6 : (insectStats.health < 30 ? 0.4 : 1);

            function applyToModel(model) {
                if (model && currentStage === 'adult') {
                    model.traverse((child) => {
                        if (child instanceof THREE.Mesh && child.material) {
                            child.material.color.multiplyScalar(colorMultiplier);
                        }
                    });
                }
            }

            applyToModel(currentModel);
            multipleInsects.forEach(insect => applyToModel(insect.model));
        }

        // 多昆虫功能
        window.addMultipleInsects = () => {
            // 清除现有昆虫
            clearExistingInsects();

            const types = ['cockroach', 'beetle'];
            const count = 8; // 增加昆虫数量
            let loadedCount = 0;

            log(`🐜 开始添加 ${count} 只昆虫...`);

            for (let i = 0; i < count; i++) {
                const type = types[i % types.length];
                const modelPath = `/models/${type}.fbx`;

                fbxLoader.load(
                    modelPath,
                    (fbx) => {
                        // 处理模型
                        const box = new THREE.Box3().setFromObject(fbx);
                        const size = box.getSize(new THREE.Vector3());
                        const center = box.getCenter(new THREE.Vector3());

                        fbx.position.sub(center);

                        const maxDimension = Math.max(size.x, size.y, size.z);
                        const targetSize = 0.3; // 稍微小一点，更真实
                        const scale = targetSize / maxDimension;
                        fbx.scale.setScalar(scale);

                        // 设置材质和阴影
                        fbx.traverse((child) => {
                            if (child instanceof THREE.Mesh) {
                                child.castShadow = true;
                                child.receiveShadow = true;

                                // 确保材质支持光照
                                if (child.material) {
                                    if (Array.isArray(child.material)) {
                                        child.material.forEach(mat => {
                                            if (mat instanceof THREE.MeshBasicMaterial) {
                                                const lambertMat = new THREE.MeshLambertMaterial({
                                                    color: mat.color,
                                                    map: mat.map
                                                });
                                                child.material = lambertMat;
                                            }
                                        });
                                    } else if (child.material instanceof THREE.MeshBasicMaterial) {
                                        const lambertMat = new THREE.MeshLambertMaterial({
                                            color: child.material.color,
                                            map: child.material.map
                                        });
                                        child.material = lambertMat;
                                    }
                                }
                            }
                        });

                        // 更自然的分布 - 在巢穴周围
                        const nestPos = environment.nest ? environment.nest.position : new THREE.Vector3(0, 0, 0);
                        const angle = (i / count) * Math.PI * 2 + Math.random() * 0.5;
                        const radius = 2 + Math.random() * 2; // 2-4的随机半径

                        fbx.position.set(
                            nestPos.x + Math.cos(angle) * radius,
                            0.1, // 稍微抬高避免与地面重叠
                            nestPos.z + Math.sin(angle) * radius
                        );

                        // 随机朝向
                        fbx.rotation.y = Math.random() * Math.PI * 2;

                        scene.add(fbx);

                        const insect = {
                            model: fbx,
                            type: type,
                            id: `insect_${Date.now()}_${i}`,
                            ai: null // 将在AI启动时初始化
                        };

                        multipleInsects.push(insect);
                        loadedCount++;

                        log(`✅ 昆虫 ${loadedCount}/${count}: ${type} (位置: ${fbx.position.x.toFixed(1)}, ${fbx.position.z.toFixed(1)})`);

                        // 所有昆虫加载完成
                        if (loadedCount === count) {
                            log(`🎉 所有 ${count} 只昆虫加载完成！`);
                            log(`💡 现在可以点击"启动AI"开始智能行为演示`);
                        }
                    },
                    (progress) => {
                        // 加载进度
                        if (progress.lengthComputable) {
                            const percent = (progress.loaded / progress.total * 100).toFixed(1);
                            log(`📥 加载昆虫 ${i + 1}: ${percent}%`);
                        }
                    },
                    (error) => {
                        log(`❌ 加载昆虫失败 ${i + 1}/${count}: ${error.message}`);

                        // 如果FBX加载失败，创建占位符
                        const placeholder = createPlaceholderInsect(
                            type === 'cockroach' ? 0x8B4513 : 0x2F4F2F,
                            type
                        );

                        // 位置设置
                        const nestPos = environment.nest ? environment.nest.position : new THREE.Vector3(0, 0, 0);
                        const angle = (i / count) * Math.PI * 2;
                        const radius = 2 + Math.random() * 2;

                        placeholder.position.set(
                            nestPos.x + Math.cos(angle) * radius,
                            0.1,
                            nestPos.z + Math.sin(angle) * radius
                        );

                        scene.add(placeholder);

                        const insect = {
                            model: placeholder,
                            type: type,
                            id: `insect_placeholder_${i}`,
                            ai: null
                        };

                        multipleInsects.push(insect);
                        loadedCount++;

                        log(`🔄 使用占位符昆虫 ${loadedCount}/${count}: ${type}`);

                        if (loadedCount === count) {
                            log(`🎉 所有 ${count} 只昆虫加载完成（包含占位符）！`);
                        }
                    }
                );
            }
        };

        function clearExistingInsects() {
            multipleInsects.forEach(insect => {
                if (insect.model) {
                    scene.remove(insect.model);
                }
            });
            multipleInsects = [];
            log('🧹 清除了现有昆虫');
        }

        // ===== AI控制函数 =====

        window.toggleAI = () => {
            aiEnabled = !aiEnabled;
            const btn = document.getElementById('aiBtn');

            if (aiEnabled) {
                btn.textContent = '停止AI';
                btn.classList.add('active');

                // 确保有昆虫可以控制
                if (multipleInsects.length === 0) {
                    addMultipleInsects();
                }

                log('🧠 AI系统已启动');
            } else {
                btn.textContent = '启动AI';
                btn.classList.remove('active');

                // 清理AI状态
                multipleInsects.forEach(insect => {
                    if (insect.ai) {
                        // 移除状态指示器
                        const indicator = insect.model.children.find(child => child.userData.isIndicator);
                        if (indicator) {
                            insect.model.remove(indicator);
                        }
                    }
                });

                log('🧠 AI系统已停止');
            }
        };

        window.resetAI = () => {
            // 重置所有昆虫的AI状态
            multipleInsects.forEach(insect => {
                if (insect.ai) {
                    insect.ai.state = 'exploring';
                    insect.ai.target = null;
                    insect.ai.energy = 100;
                    insect.ai.carryingFood = false;
                    insect.ai.memory = {
                        knownFoodSources: [],
                        visitedLocations: [],
                        pheromoneStrength: 0
                    };
                }
            });

            log('🔄 AI状态已重置');
        };

        window.updateForaging = () => {
            const slider = document.getElementById('foragingSlider');
            const display = document.getElementById('foragingValue');
            aiSettings.foraging = parseInt(slider.value);
            display.textContent = aiSettings.foraging;
            log(`🍯 觅食欲望设置为: ${aiSettings.foraging}`);
        };

        window.updateSocial = () => {
            const slider = document.getElementById('socialSlider');
            const display = document.getElementById('socialValue');
            aiSettings.social = parseInt(slider.value);
            display.textContent = aiSettings.social;
            log(`👥 社交性设置为: ${aiSettings.social}`);
        };

        window.updateExploration = () => {
            const slider = document.getElementById('explorationSlider');
            const display = document.getElementById('explorationValue');
            aiSettings.exploration = parseInt(slider.value);
            display.textContent = aiSettings.exploration;
            log(`🔍 探索欲设置为: ${aiSettings.exploration}`);
        };

        // ===== 环境控制函数 =====

        window.addFoodSources = () => {
            // 清除现有食物源
            environment.foodSources.forEach(food => {
                if (food.visual) scene.remove(food.visual);
            });
            environment.foodSources = [];

            const count = parseInt(document.getElementById('foodCountSlider').value);

            log(`🍯 添加 ${count} 个食物源...`);

            for (let i = 0; i < count; i++) {
                // 更智能的食物分布 - 避免太靠近巢穴，但也不要太远
                let position;
                let attempts = 0;

                do {
                    const angle = Math.random() * Math.PI * 2;
                    const distance = 3 + Math.random() * (environment.size - 3); // 距离巢穴3-size单位

                    position = new THREE.Vector3(
                        Math.cos(angle) * distance,
                        0.2,
                        Math.sin(angle) * distance
                    );
                    attempts++;
                } while (attempts < 10); // 最多尝试10次

                const foodSource = {
                    position: position,
                    amount: 100,
                    type: 'food',
                    id: `food_${i}`
                };

                environment.foodSources.push(foodSource);

                // 创建更吸引人的视觉表示
                const geometry = new THREE.SphereGeometry(0.25);
                const material = new THREE.MeshLambertMaterial({
                    color: 0x32CD32, // 更鲜艳的绿色
                    emissive: 0x001100 // 轻微发光
                });
                const mesh = new THREE.Mesh(geometry, material);
                mesh.position.copy(position);
                mesh.castShadow = true;
                mesh.receiveShadow = true;
                mesh.userData.isFood = true;
                mesh.userData.foodId = foodSource.id;

                // 添加轻微的浮动动画
                mesh.userData.originalY = position.y;
                mesh.userData.floatOffset = Math.random() * Math.PI * 2;

                scene.add(mesh);
                foodSource.visual = mesh;

                log(`  🟢 食物 ${i + 1}: (${position.x.toFixed(1)}, ${position.z.toFixed(1)})`);
            }

            log(`✅ ${count} 个食物源添加完成`);
        };

        window.addObstacles = () => {
            const count = 3;

            for (let i = 0; i < count; i++) {
                const position = new THREE.Vector3(
                    (Math.random() - 0.5) * environment.size * 1.5,
                    0.5,
                    (Math.random() - 0.5) * environment.size * 1.5
                );

                const obstacle = {
                    position: position,
                    type: 'obstacle'
                };

                environment.obstacles.push(obstacle);

                // 创建视觉表示
                const geometry = new THREE.BoxGeometry(1, 1, 1);
                const material = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
                const mesh = new THREE.Mesh(geometry, material);
                mesh.position.copy(position);
                mesh.castShadow = true;
                mesh.userData.isObstacle = true;

                scene.add(mesh);
                obstacle.visual = mesh;
            }

            log(`🚧 添加了 ${count} 个障碍物`);
        };

        window.addNest = () => {
            // 移除旧巢穴
            if (environment.nest && environment.nest.visual) {
                scene.remove(environment.nest.visual);
            }

            const position = new THREE.Vector3(0, 0.3, 0);

            environment.nest = {
                position: position,
                type: 'nest'
            };

            // 创建更复杂的巢穴视觉表示
            const nestGroup = new THREE.Group();

            // 主体 - 圆锥形
            const mainGeometry = new THREE.CylinderGeometry(0.8, 1.2, 0.6, 8);
            const mainMaterial = new THREE.MeshLambertMaterial({
                color: 0x8B4513,
                emissive: 0x221100 // 轻微发光
            });
            const mainMesh = new THREE.Mesh(mainGeometry, mainMaterial);
            mainMesh.castShadow = true;
            mainMesh.receiveShadow = true;
            nestGroup.add(mainMesh);

            // 入口 - 黑色洞口
            const entranceGeometry = new THREE.CylinderGeometry(0.3, 0.3, 0.1, 8);
            const entranceMaterial = new THREE.MeshLambertMaterial({ color: 0x000000 });
            const entranceMesh = new THREE.Mesh(entranceGeometry, entranceMaterial);
            entranceMesh.position.y = 0.35;
            nestGroup.add(entranceMesh);

            // 装饰环
            const ringGeometry = new THREE.TorusGeometry(1.0, 0.1, 8, 16);
            const ringMaterial = new THREE.MeshLambertMaterial({ color: 0x654321 });
            const ringMesh = new THREE.Mesh(ringGeometry, ringMaterial);
            ringMesh.position.y = -0.2;
            ringMesh.rotation.x = Math.PI / 2;
            nestGroup.add(ringMesh);

            nestGroup.position.copy(position);
            nestGroup.userData.isNest = true;

            scene.add(nestGroup);
            environment.nest.visual = nestGroup;

            log('🏠 添加了巢穴 (位置: 中心)');
        };

        window.clearEnvironment = () => {
            // 清除食物源
            environment.foodSources.forEach(food => {
                if (food.visual) scene.remove(food.visual);
            });
            environment.foodSources = [];

            // 清除障碍物
            environment.obstacles.forEach(obstacle => {
                if (obstacle.visual) scene.remove(obstacle.visual);
            });
            environment.obstacles = [];

            // 清除巢穴
            if (environment.nest && environment.nest.visual) {
                scene.remove(environment.nest.visual);
            }
            environment.nest = null;

            // 清除信息素
            clearPheromones();

            log('🧹 环境已清空');
        };

        window.addPheromoneTrail = () => {
            const count = 10;
            const centerX = (Math.random() - 0.5) * environment.size;
            const centerZ = (Math.random() - 0.5) * environment.size;

            for (let i = 0; i < count; i++) {
                const angle = (i / count) * Math.PI * 2;
                const radius = 1 + Math.random() * 2;

                const position = new THREE.Vector3(
                    centerX + Math.cos(angle) * radius,
                    0,
                    centerZ + Math.sin(angle) * radius
                );

                addPheromoneAt(position, 'food');
            }

            log(`💨 添加了信息素轨迹`);
        };

        window.clearPheromones = () => {
            environment.pheromoneTrails.forEach(pheromone => {
                if (pheromone.visual) {
                    scene.remove(pheromone.visual);
                }
            });
            environment.pheromoneTrails = [];

            log('💨 信息素已清除');
        };

        window.updateFoodCount = () => {
            const slider = document.getElementById('foodCountSlider');
            const display = document.getElementById('foodCountValue');
            display.textContent = slider.value;
        };

        window.updateEnvironmentSize = () => {
            const slider = document.getElementById('environmentSizeSlider');
            const display = document.getElementById('environmentSizeValue');
            environment.size = parseInt(slider.value);
            display.textContent = environment.size;
            log(`🌍 环境大小设置为: ${environment.size}`);
        };

        // 导出功能
        window.exportModel = () => {
            const info = {
                currentModel: currentModel ? 'loaded' : 'none',
                multipleInsects: multipleInsects.length,
                stage: currentStage,
                stats: insectStats,
                animation: {
                    enabled: animationEnabled,
                    speed: animationSpeed,
                    rotationSpeed: rotationSpeed,
                    jumpHeight: jumpHeight
                },
                wireframe: wireframeMode,
                ai: {
                    enabled: aiEnabled,
                    settings: aiSettings,
                    environment: {
                        foodSources: environment.foodSources.length,
                        obstacles: environment.obstacles.length,
                        hasNest: environment.nest !== null,
                        pheromoneTrails: environment.pheromoneTrails.length,
                        size: environment.size
                    }
                }
            };

            const dataStr = JSON.stringify(info, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = 'model-viewer-export.json';
            link.click();

            URL.revokeObjectURL(url);
            log('模型信息已导出');
        };

        // 初始化UI状态
        function initializeUI() {
            // 设置默认活跃的生命阶段按钮
            document.querySelector('[data-stage="adult"]').classList.add('active');

            // 设置滑块的初始显示值
            document.getElementById('speedValue').textContent = '1.0';
            document.getElementById('rotationValue').textContent = '1.0';
            document.getElementById('jumpValue').textContent = '0.1';
            document.getElementById('healthValue').textContent = '100';
            document.getElementById('hungerValue').textContent = '50';
            document.getElementById('happinessValue').textContent = '75';

            // AI控制滑块
            document.getElementById('foragingValue').textContent = '70';
            document.getElementById('socialValue').textContent = '50';
            document.getElementById('explorationValue').textContent = '60';

            // 环境控制滑块
            document.getElementById('foodCountValue').textContent = '5';
            document.getElementById('environmentSizeValue').textContent = '10';
        }

        // 自动设置演示场景
        function setupDemoScene() {
            log('🎬 正在设置演示场景...');

            // 延迟执行，确保场景完全初始化
            setTimeout(() => {
                // 1. 添加巢穴
                addNest();

                // 2. 添加食物源
                addFoodSources();

                // 3. 添加一些障碍物
                addObstacles();

                // 4. 添加昆虫
                setTimeout(() => {
                    addMultipleInsects();

                    // 5. 自动启动基础动画，让场景立即生动起来
                    setTimeout(() => {
                        if (!animationEnabled) {
                            toggleAnimation();
                        }

                        log('✨ 演示场景设置完成！');
                        log('🎬 基础动画已启动');
                        log('🧠 点击"启动AI"按钮开始智能行为演示');
                        log('� 点击"添加信息素轨迹"查看信息素效果');

                        // 可选：自动启动AI（取消注释下面的代码）
                        // setTimeout(() => {
                        //     if (!aiEnabled) {
                        //         toggleAI();
                        //         log('🤖 AI已自动启动！');
                        //     }
                        // }, 2000);
                    }, 1000);
                }, 500);
            }, 1000);
        }

        // 启动
        init();
        initializeUI();
        setupDemoScene();
    </script>
</body>
</html>
