<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>昆虫养殖游戏</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow: hidden;
        }

        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #gameCanvas {
            display: block;
            width: 100%;
            height: 100%;
        }

        #ui {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 100;
        }

        .ui-panel {
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 10px;
            padding: 15px;
            color: white;
            pointer-events: auto;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        #playerInfo {
            top: 20px;
            left: 20px;
            min-width: 200px;
        }

        #gameControls {
            top: 20px;
            right: 20px;
            min-width: 250px;
        }

        #breedingBoxes {
            bottom: 20px;
            left: 20px;
            right: 20px;
            max-height: 200px;
            overflow-y: auto;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
            padding: 5px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
        }

        .button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            border: none;
            color: white;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 5px;
            transition: all 0.3s;
        }

        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }

        .breeding-box {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 10px;
            margin: 10px 0;
            border-left: 4px solid #4CAF50;
        }

        .breeding-box h4 {
            color: #4CAF50;
            margin-bottom: 8px;
        }

        .insect-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
            gap: 5px;
            margin-top: 10px;
        }

        .insect-item {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 5px;
            padding: 5px;
            text-align: center;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .insect-item:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        .insect-egg { border-left: 3px solid #FFE082; }
        .insect-larva { border-left: 3px solid #FFCC02; }
        .insect-pupa { border-left: 3px solid #FF9800; }
        .insect-adult { border-left: 3px solid #4CAF50; }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 15% auto;
            padding: 20px;
            border-radius: 10px;
            width: 80%;
            max-width: 500px;
            color: #333;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: black;
        }

        #loadingScreen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            color: white;
            font-size: 24px;
        }

        .loading-spinner {
            border: 4px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top: 4px solid white;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin-right: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div id="loadingScreen">
        <div class="loading-spinner"></div>
        <div>加载中...</div>
    </div>

    <div id="gameContainer">
        <canvas id="gameCanvas"></canvas>
        
        <div id="ui">
            <!-- 玩家信息面板 -->
            <div id="playerInfo" class="ui-panel">
                <h3>玩家信息</h3>
                <div class="stat-item">
                    <span>金币:</span>
                    <span id="playerMoney">0</span>
                </div>
                <div class="stat-item">
                    <span>等级:</span>
                    <span id="playerLevel">1</span>
                </div>
                <div class="stat-item">
                    <span>经验:</span>
                    <span id="playerExp">0</span>
                </div>
            </div>

            <!-- 游戏控制面板 -->
            <div id="gameControls" class="ui-panel">
                <h3>游戏控制</h3>
                <button id="buyBoxBtn" class="button">购买养殖箱</button>
                <button id="feedAllBtn" class="button">喂食所有</button>
                <button id="breedBtn" class="button">繁殖昆虫</button>
                <button id="sellBtn" class="button">出售昆虫</button>
            </div>

            <!-- 养殖箱面板 -->
            <div id="breedingBoxes" class="ui-panel">
                <h3>养殖箱</h3>
                <div id="boxesList"></div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="shopModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>商店</h2>
            <div id="shopItems"></div>
        </div>
    </div>

    <div id="breedModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>繁殖昆虫</h2>
            <p>选择一对成虫进行繁殖</p>
            <div id="breedSelection"></div>
            <button id="confirmBreed" class="button">确认繁殖</button>
        </div>
    </div>

    <script type="module" src="/src/main.ts"></script>
</body>
</html>
