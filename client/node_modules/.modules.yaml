hoistPattern:
  - '*'
hoistedDependencies:
  '@esbuild/aix-ppc64@0.21.5':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.21.5':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.21.5':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.21.5':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.21.5':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.21.5':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.21.5':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.21.5':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.21.5':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.21.5':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.21.5':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.21.5':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.21.5':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.21.5':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.21.5':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.21.5':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.21.5':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-x64@0.21.5':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-x64@0.21.5':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.21.5':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.21.5':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.21.5':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.21.5':
    '@esbuild/win32-x64': private
  '@rollup/rollup-android-arm-eabi@4.43.0':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.43.0':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.43.0':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.43.0':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.43.0':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.43.0':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.43.0':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.43.0':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.43.0':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.43.0':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.43.0':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.43.0':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.43.0':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.43.0':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.43.0':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.43.0':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.43.0':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.43.0':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.43.0':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.43.0':
    '@rollup/rollup-win32-x64-msvc': private
  '@socket.io/component-emitter@3.1.2':
    '@socket.io/component-emitter': private
  '@types/estree@1.0.7':
    '@types/estree': private
  '@types/stats.js@0.17.4':
    '@types/stats.js': private
  '@types/webxr@0.5.22':
    '@types/webxr': private
  debug@4.3.7:
    debug: private
  engine.io-client@6.6.3:
    engine.io-client: private
  engine.io-parser@5.2.3:
    engine.io-parser: private
  esbuild@0.21.5:
    esbuild: private
  fflate@0.6.10:
    fflate: private
  fsevents@2.3.3:
    fsevents: private
  meshoptimizer@0.18.1:
    meshoptimizer: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.11:
    nanoid: private
  picocolors@1.1.1:
    picocolors: private
  postcss@8.5.5:
    postcss: private
  rollup@4.43.0:
    rollup: private
  socket.io-parser@4.2.4:
    socket.io-parser: private
  source-map-js@1.2.1:
    source-map-js: private
  ws@8.17.1:
    ws: private
  xmlhttprequest-ssl@2.1.2:
    xmlhttprequest-ssl: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.15.0
pendingBuilds: []
prunedAt: Mon, 16 Jun 2025 08:34:15 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.21.5'
  - '@esbuild/android-arm64@0.21.5'
  - '@esbuild/android-arm@0.21.5'
  - '@esbuild/android-x64@0.21.5'
  - '@esbuild/darwin-x64@0.21.5'
  - '@esbuild/freebsd-arm64@0.21.5'
  - '@esbuild/freebsd-x64@0.21.5'
  - '@esbuild/linux-arm64@0.21.5'
  - '@esbuild/linux-arm@0.21.5'
  - '@esbuild/linux-ia32@0.21.5'
  - '@esbuild/linux-loong64@0.21.5'
  - '@esbuild/linux-mips64el@0.21.5'
  - '@esbuild/linux-ppc64@0.21.5'
  - '@esbuild/linux-riscv64@0.21.5'
  - '@esbuild/linux-s390x@0.21.5'
  - '@esbuild/linux-x64@0.21.5'
  - '@esbuild/netbsd-x64@0.21.5'
  - '@esbuild/openbsd-x64@0.21.5'
  - '@esbuild/sunos-x64@0.21.5'
  - '@esbuild/win32-arm64@0.21.5'
  - '@esbuild/win32-ia32@0.21.5'
  - '@esbuild/win32-x64@0.21.5'
  - '@rollup/rollup-android-arm-eabi@4.43.0'
  - '@rollup/rollup-android-arm64@4.43.0'
  - '@rollup/rollup-darwin-x64@4.43.0'
  - '@rollup/rollup-freebsd-arm64@4.43.0'
  - '@rollup/rollup-freebsd-x64@4.43.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.43.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.43.0'
  - '@rollup/rollup-linux-arm64-gnu@4.43.0'
  - '@rollup/rollup-linux-arm64-musl@4.43.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.43.0'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.43.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.43.0'
  - '@rollup/rollup-linux-riscv64-musl@4.43.0'
  - '@rollup/rollup-linux-s390x-gnu@4.43.0'
  - '@rollup/rollup-linux-x64-gnu@4.43.0'
  - '@rollup/rollup-linux-x64-musl@4.43.0'
  - '@rollup/rollup-win32-arm64-msvc@4.43.0'
  - '@rollup/rollup-win32-ia32-msvc@4.43.0'
  - '@rollup/rollup-win32-x64-msvc@4.43.0'
storeDir: /Users/<USER>/Library/pnpm/store/v3
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
