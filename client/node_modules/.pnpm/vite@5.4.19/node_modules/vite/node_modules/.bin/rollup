#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/test-augment/client/node_modules/.pnpm/rollup@4.43.0/node_modules/rollup/dist/bin/node_modules:/Users/<USER>/test-augment/client/node_modules/.pnpm/rollup@4.43.0/node_modules/rollup/dist/node_modules:/Users/<USER>/test-augment/client/node_modules/.pnpm/rollup@4.43.0/node_modules/rollup/node_modules:/Users/<USER>/test-augment/client/node_modules/.pnpm/rollup@4.43.0/node_modules:/Users/<USER>/test-augment/client/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/test-augment/client/node_modules/.pnpm/rollup@4.43.0/node_modules/rollup/dist/bin/node_modules:/Users/<USER>/test-augment/client/node_modules/.pnpm/rollup@4.43.0/node_modules/rollup/dist/node_modules:/Users/<USER>/test-augment/client/node_modules/.pnpm/rollup@4.43.0/node_modules/rollup/node_modules:/Users/<USER>/test-augment/client/node_modules/.pnpm/rollup@4.43.0/node_modules:/Users/<USER>/test-augment/client/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../rollup@4.43.0/node_modules/rollup/dist/bin/rollup" "$@"
else
  exec node  "$basedir/../../../../../rollup@4.43.0/node_modules/rollup/dist/bin/rollup" "$@"
fi
