import { Insect, BreedingBox, Player, LifeStage } from '../../../shared/types';

export class UIManager extends EventTarget {
  private gameState: {
    player?: Player;
    insects: Insect[];
    breedingBoxes: BreedingBox[];
  } = {
    insects: [],
    breedingBoxes: []
  };

  private selectedInsects: string[] = [];

  constructor() {
    super();
  }

  init(): void {
    this.setupEventListeners();
    this.setupGlobalMethods();
    console.log('UIManager 初始化完成');
  }

  private setupEventListeners(): void {
    // 购买养殖箱按钮
    const buyBoxBtn = document.getElementById('buyBoxBtn');
    buyBoxBtn?.addEventListener('click', () => {
      this.showShopModal();
    });

    // 喂食所有按钮
    const feedAllBtn = document.getElementById('feedAllBtn');
    feedAllBtn?.addEventListener('click', () => {
      this.feedAllInsects();
    });

    // 繁殖按钮
    const breedBtn = document.getElementById('breedBtn');
    breedBtn?.addEventListener('click', () => {
      this.showBreedModal();
    });

    // 出售按钮
    const sellBtn = document.getElementById('sellBtn');
    sellBtn?.addEventListener('click', () => {
      this.sellSelectedInsects();
    });

    // 模态框关闭按钮
    const closeButtons = document.querySelectorAll('.close');
    closeButtons.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const modal = (e.target as HTMLElement).closest('.modal') as HTMLElement;
        if (modal) {
          modal.style.display = 'none';
        }
      });
    });

    // 点击模态框外部关闭
    window.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      if (target.classList.contains('modal')) {
        target.style.display = 'none';
      }
    });
  }

  updateGameState(gameState: any): void {
    this.gameState = gameState;
    this.updateUI();
  }

  private updateUI(): void {
    this.updatePlayerInfo();
    this.updateBreedingBoxes();
  }

  private updatePlayerInfo(): void {
    if (!this.gameState.player) return;

    const player = this.gameState.player;
    
    const moneyElement = document.getElementById('playerMoney');
    const levelElement = document.getElementById('playerLevel');
    const expElement = document.getElementById('playerExp');

    if (moneyElement) moneyElement.textContent = player.money.toString();
    if (levelElement) levelElement.textContent = player.level.toString();
    if (expElement) expElement.textContent = player.experience.toString();
  }

  private updateBreedingBoxes(): void {
    const boxesList = document.getElementById('boxesList');
    if (!boxesList) return;

    boxesList.innerHTML = '';

    for (const box of this.gameState.breedingBoxes) {
      const boxElement = this.createBreedingBoxElement(box);
      boxesList.appendChild(boxElement);
    }
  }

  private createBreedingBoxElement(box: BreedingBox): HTMLElement {
    const boxDiv = document.createElement('div');
    boxDiv.className = 'breeding-box';
    boxDiv.innerHTML = `
      <h4>${box.name}</h4>
      <div class="stat-item">
        <span>容量:</span>
        <span>${box.currentCount}/${box.capacity}</span>
      </div>
      <div class="stat-item">
        <span>温度:</span>
        <span>${box.temperature.toFixed(1)}°C</span>
      </div>
      <div class="stat-item">
        <span>湿度:</span>
        <span>${box.humidity.toFixed(1)}%</span>
      </div>
      <div class="stat-item">
        <span>清洁度:</span>
        <span>${box.cleanliness}/100</span>
      </div>
      <div class="stat-item">
        <span>食物:</span>
        <span>${box.foodLevel}/100</span>
      </div>
      <div class="insect-grid" id="insects-${box.id}"></div>
      <button class="button" onclick="this.feedBox('${box.id}')">喂食 (10金币)</button>
    `;

    // 添加昆虫
    const insectGrid = boxDiv.querySelector(`#insects-${box.id}`) as HTMLElement;
    const boxInsects = this.gameState.insects.filter(insect => insect.breedingBoxId === box.id);
    
    for (const insect of boxInsects) {
      const insectElement = this.createInsectElement(insect);
      insectGrid.appendChild(insectElement);
    }

    return boxDiv;
  }

  private createInsectElement(insect: Insect): HTMLElement {
    const insectDiv = document.createElement('div');
    insectDiv.className = `insect-item insect-${insect.stage}`;
    insectDiv.innerHTML = `
      <div>${this.getInsectEmoji(insect)}</div>
      <div style="font-size: 10px;">${this.getStageText(insect.stage)}</div>
      <div style="font-size: 9px;">♥${insect.health}</div>
    `;

    insectDiv.addEventListener('click', () => {
      this.toggleInsectSelection(insect.id, insectDiv);
    });

    return insectDiv;
  }

  private getInsectEmoji(insect: Insect): string {
    if (insect.type === 'cockroach') {
      switch (insect.stage) {
        case 'egg': return '🥚';
        case 'larva': return '🐛';
        case 'pupa': return '🛡️';
        case 'adult': return '🪳';
        default: return '🪳';
      }
    } else if (insect.type === 'beetle') {
      switch (insect.stage) {
        case 'egg': return '🥚';
        case 'larva': return '🐛';
        case 'pupa': return '🛡️';
        case 'adult': return '🪲';
        default: return '🪲';
      }
    }
    return '🐛';
  }

  private getStageText(stage: LifeStage): string {
    switch (stage) {
      case 'egg': return '卵';
      case 'larva': return '幼虫';
      case 'pupa': return '蛹';
      case 'adult': return '成虫';
      default: return '未知';
    }
  }

  private toggleInsectSelection(insectId: string, element: HTMLElement): void {
    const index = this.selectedInsects.indexOf(insectId);
    if (index > -1) {
      this.selectedInsects.splice(index, 1);
      element.style.border = '';
    } else {
      this.selectedInsects.push(insectId);
      element.style.border = '2px solid #4CAF50';
    }
  }

  private showShopModal(): void {
    const modal = document.getElementById('shopModal');
    const shopItems = document.getElementById('shopItems');
    
    if (!modal || !shopItems) return;

    // 获取商店物品
    fetch('/api/shop')
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          shopItems.innerHTML = '';
          for (const item of data.data) {
            const itemElement = document.createElement('div');
            itemElement.className = 'shop-item';
            itemElement.innerHTML = `
              <h4>${item.name}</h4>
              <p>${item.description}</p>
              <p>价格: ${item.price} 金币</p>
              <button class="button" onclick="this.purchaseItem('${item.id}')">购买</button>
            `;
            shopItems.appendChild(itemElement);
          }
        }
      });

    modal.style.display = 'block';
  }

  private showBreedModal(): void {
    const modal = document.getElementById('breedModal');
    const breedSelection = document.getElementById('breedSelection');
    
    if (!modal || !breedSelection) return;

    // 获取可繁殖的成虫
    const adults = this.gameState.insects.filter(insect => 
      insect.stage === 'adult' && insect.health > 70
    );

    const males = adults.filter(insect => insect.gender === 'male');
    const females = adults.filter(insect => insect.gender === 'female');

    breedSelection.innerHTML = `
      <div>
        <h4>选择雄性昆虫:</h4>
        <select id="maleSelect">
          <option value="">请选择...</option>
          ${males.map(insect => 
            `<option value="${insect.id}">${this.getInsectEmoji(insect)} ${insect.type} (健康: ${insect.health})</option>`
          ).join('')}
        </select>
      </div>
      <div>
        <h4>选择雌性昆虫:</h4>
        <select id="femaleSelect">
          <option value="">请选择...</option>
          ${females.map(insect => 
            `<option value="${insect.id}">${this.getInsectEmoji(insect)} ${insect.type} (健康: ${insect.health})</option>`
          ).join('')}
        </select>
      </div>
    `;

    const confirmBtn = document.getElementById('confirmBreed');
    if (confirmBtn) {
      confirmBtn.onclick = () => {
        const maleSelect = document.getElementById('maleSelect') as HTMLSelectElement;
        const femaleSelect = document.getElementById('femaleSelect') as HTMLSelectElement;
        
        if (maleSelect.value && femaleSelect.value) {
          this.dispatchEvent(new CustomEvent('breedInsects', {
            detail: { maleId: maleSelect.value, femaleId: femaleSelect.value }
          }));
          modal.style.display = 'none';
        } else {
          alert('请选择一对昆虫进行繁殖');
        }
      };
    }

    modal.style.display = 'block';
  }

  private feedAllInsects(): void {
    for (const box of this.gameState.breedingBoxes) {
      if (box.currentCount > 0) {
        this.dispatchEvent(new CustomEvent('feedInsects', {
          detail: box.id
        }));
      }
    }
  }

  private sellSelectedInsects(): void {
    if (this.selectedInsects.length === 0) {
      alert('请先选择要出售的昆虫');
      return;
    }

    for (const insectId of this.selectedInsects) {
      this.dispatchEvent(new CustomEvent('sellInsect', {
        detail: insectId
      }));
    }

    this.selectedInsects = [];
  }

  showInsectDetails(insectId: string): void {
    const insect = this.gameState.insects.find(i => i.id === insectId);
    if (!insect) return;

    alert(`昆虫详情:
类型: ${insect.type}
阶段: ${this.getStageText(insect.stage)}
性别: ${insect.gender === 'male' ? '雄性' : '雌性'}
年龄: ${Math.floor(insect.age)}小时
健康: ${insect.health}/100
饥饿: ${insect.hunger}/100
快乐: ${insect.happiness}/100
价值: ${insect.price}金币`);
  }

  showBreedingBoxDetails(boxId: string): void {
    const box = this.gameState.breedingBoxes.find(b => b.id === boxId);
    if (!box) return;

    alert(`养殖箱详情:
名称: ${box.name}
容量: ${box.currentCount}/${box.capacity}
温度: ${box.temperature.toFixed(1)}°C
湿度: ${box.humidity.toFixed(1)}%
清洁度: ${box.cleanliness}/100
食物水平: ${box.foodLevel}/100
维护费用: ${box.maintenanceCost}金币/小时`);
  }

  // 设置全局方法，供HTML调用
  setupGlobalMethods(): void {
    (window as any).feedBox = (boxId: string) => {
      this.dispatchEvent(new CustomEvent('feedInsects', {
        detail: boxId
      }));
    };

    (window as any).purchaseItem = (itemId: string) => {
      this.dispatchEvent(new CustomEvent('purchaseBreedingBox', {
        detail: itemId.replace('-box', '')
      }));

      const modal = document.getElementById('shopModal');
      if (modal) modal.style.display = 'none';
    };
  }
}
