import { GameEngine } from './game/GameEngine';
import { UIManager } from './ui/UIManager';
import { NetworkManager } from './network/NetworkManager';

class InsectFarmingGame {
  private gameEngine: GameEngine;
  private uiManager: UIManager;
  private networkManager: NetworkManager;
  private isInitialized = false;

  constructor() {
    this.gameEngine = new GameEngine();
    this.uiManager = new UIManager();
    this.networkManager = new NetworkManager();
  }

  async init(): Promise<void> {
    try {
      console.log('初始化游戏...');
      
      // 初始化游戏引擎
      await this.gameEngine.init();
      console.log('游戏引擎初始化完成');

      // 初始化UI管理器
      this.uiManager.init();
      console.log('UI管理器初始化完成');

      // 初始化网络管理器
      await this.networkManager.init();
      console.log('网络管理器初始化完成');

      // 设置事件监听
      this.setupEventListeners();

      // 连接到游戏服务器
      const playerId = this.getOrCreatePlayerId();
      await this.networkManager.joinGame(playerId);

      this.isInitialized = true;
      this.hideLoadingScreen();
      
      console.log('游戏初始化完成');
    } catch (error) {
      console.error('游戏初始化失败:', error);
      this.showError('游戏初始化失败，请刷新页面重试');
    }
  }

  private setupEventListeners(): void {
    // 网络事件
    this.networkManager.on('gameStateUpdate', (gameState) => {
      this.uiManager.updateGameState(gameState);
      this.gameEngine.updateGameState(gameState);
    });

    this.networkManager.on('error', (error) => {
      this.showError(error);
    });

    // UI事件
    this.uiManager.on('purchaseBreedingBox', (boxType) => {
      this.networkManager.purchaseBreedingBox(boxType);
    });

    this.uiManager.on('feedInsects', (breedingBoxId) => {
      this.networkManager.feedInsects(breedingBoxId);
    });

    this.uiManager.on('breedInsects', (data) => {
      this.networkManager.breedInsects(data.maleId, data.femaleId);
    });

    this.uiManager.on('sellInsect', (insectId) => {
      this.networkManager.sellInsect(insectId);
    });

    // 游戏引擎事件
    this.gameEngine.on('insectClicked', (insectId) => {
      this.uiManager.showInsectDetails(insectId);
    });

    this.gameEngine.on('breedingBoxClicked', (boxId) => {
      this.uiManager.showBreedingBoxDetails(boxId);
    });

    // 窗口事件
    window.addEventListener('resize', () => {
      this.gameEngine.handleResize();
    });

    window.addEventListener('beforeunload', () => {
      this.networkManager.disconnect();
    });
  }

  private getOrCreatePlayerId(): string {
    let playerId = localStorage.getItem('insectFarmingPlayerId');
    if (!playerId) {
      playerId = 'player_' + Math.random().toString(36).substring(2, 11);
      localStorage.setItem('insectFarmingPlayerId', playerId);
    }
    return playerId;
  }

  private hideLoadingScreen(): void {
    const loadingScreen = document.getElementById('loadingScreen');
    if (loadingScreen) {
      loadingScreen.style.display = 'none';
    }
  }

  private showError(message: string): void {
    alert(`错误: ${message}`);
  }

  start(): void {
    if (!this.isInitialized) {
      console.error('游戏未初始化');
      return;
    }

    this.gameEngine.start();
    console.log('游戏开始运行');
  }
}

// 启动游戏
const game = new InsectFarmingGame();

// 等待DOM加载完成
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', async () => {
    await game.init();
    game.start();
  });
} else {
  // DOM已经加载完成
  game.init().then(() => {
    game.start();
  });
}
