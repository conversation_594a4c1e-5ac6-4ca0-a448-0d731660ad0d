import { io, Socket } from 'socket.io-client';

export class NetworkManager extends EventTarget {
  private socket: Socket | null = null;
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  constructor() {
    super();
  }

  async init(): Promise<void> {
    try {
      this.socket = io('http://localhost:3006', {
        transports: ['websocket', 'polling']
      });

      this.setupSocketListeners();
      
      // 等待连接建立
      await this.waitForConnection();
      
      console.log('NetworkManager 初始化完成');
    } catch (error) {
      console.error('网络管理器初始化失败:', error);
      throw error;
    }
  }

  private setupSocketListeners(): void {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('已连接到服务器');
      this.isConnected = true;
      this.reconnectAttempts = 0;
    });

    this.socket.on('disconnect', () => {
      console.log('与服务器断开连接');
      this.isConnected = false;
      this.attemptReconnect();
    });

    this.socket.on('connect_error', (error) => {
      console.error('连接错误:', error);
      this.dispatchEvent(new CustomEvent('error', {
        detail: '无法连接到服务器'
      }));
    });

    // 游戏事件监听
    this.socket.on('gameJoined', (data) => {
      if (data.success) {
        console.log('成功加入游戏');
      } else {
        console.error('加入游戏失败:', data.error);
        this.dispatchEvent(new CustomEvent('error', {
          detail: data.error
        }));
      }
    });

    this.socket.on('gameStateUpdate', (gameState) => {
      this.dispatchEvent(new CustomEvent('gameStateUpdate', {
        detail: gameState
      }));
    });

    this.socket.on('purchaseResult', (result) => {
      if (result.success) {
        console.log('购买成功:', result.message || '购买成功');
        this.showMessage('购买成功！');
      } else {
        console.error('购买失败:', result.error);
        this.dispatchEvent(new CustomEvent('error', {
          detail: result.error
        }));
      }
    });

    this.socket.on('feedResult', (result) => {
      if (result.success) {
        console.log('喂食成功:', result.message);
        this.showMessage(result.message);
      } else {
        console.error('喂食失败:', result.error);
        this.dispatchEvent(new CustomEvent('error', {
          detail: result.error
        }));
      }
    });

    this.socket.on('breedResult', (result) => {
      if (result.success) {
        console.log('繁殖成功:', result.message);
        this.showMessage(result.message);
      } else {
        console.error('繁殖失败:', result.error);
        this.dispatchEvent(new CustomEvent('error', {
          detail: result.error
        }));
      }
    });

    this.socket.on('sellResult', (result) => {
      if (result.success) {
        console.log('出售成功:', result.message);
        this.showMessage(result.message);
        if (result.levelUp) {
          this.showMessage('恭喜升级！');
        }
      } else {
        console.error('出售失败:', result.error);
        this.dispatchEvent(new CustomEvent('error', {
          detail: result.error
        }));
      }
    });
  }

  private waitForConnection(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Socket未初始化'));
        return;
      }

      if (this.socket.connected) {
        resolve();
        return;
      }

      const timeout = setTimeout(() => {
        reject(new Error('连接超时'));
      }, 10000); // 10秒超时

      this.socket.once('connect', () => {
        clearTimeout(timeout);
        resolve();
      });

      this.socket.once('connect_error', (error) => {
        clearTimeout(timeout);
        reject(error);
      });
    });
  }

  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('达到最大重连次数，停止重连');
      this.dispatchEvent(new CustomEvent('error', {
        detail: '无法重新连接到服务器，请刷新页面'
      }));
      return;
    }

    this.reconnectAttempts++;
    console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

    setTimeout(() => {
      if (this.socket && !this.socket.connected) {
        this.socket.connect();
      }
    }, 2000 * this.reconnectAttempts); // 递增延迟
  }

  async joinGame(playerId: string): Promise<void> {
    if (!this.socket || !this.isConnected) {
      throw new Error('未连接到服务器');
    }

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('加入游戏超时'));
      }, 5000);

      this.socket!.once('gameJoined', (data) => {
        clearTimeout(timeout);
        if (data.success) {
          resolve();
        } else {
          reject(new Error(data.error));
        }
      });

      this.socket!.emit('joinGame', playerId);
    });
  }

  purchaseBreedingBox(boxType: string): void {
    if (!this.socket || !this.isConnected) {
      this.dispatchEvent(new CustomEvent('error', {
        detail: '未连接到服务器'
      }));
      return;
    }

    this.socket.emit('purchaseBreedingBox', { boxType });
  }

  feedInsects(breedingBoxId: string): void {
    if (!this.socket || !this.isConnected) {
      this.dispatchEvent(new CustomEvent('error', {
        detail: '未连接到服务器'
      }));
      return;
    }

    this.socket.emit('feedInsects', { breedingBoxId });
  }

  breedInsects(maleId: string, femaleId: string): void {
    if (!this.socket || !this.isConnected) {
      this.dispatchEvent(new CustomEvent('error', {
        detail: '未连接到服务器'
      }));
      return;
    }

    this.socket.emit('breedInsects', { maleId, femaleId });
  }

  sellInsect(insectId: string): void {
    if (!this.socket || !this.isConnected) {
      this.dispatchEvent(new CustomEvent('error', {
        detail: '未连接到服务器'
      }));
      return;
    }

    this.socket.emit('sellInsect', { insectId });
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
    }
  }

  private showMessage(message: string): void {
    // 创建临时消息提示
    const messageDiv = document.createElement('div');
    messageDiv.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 20px;
      border-radius: 10px;
      z-index: 10000;
      font-size: 16px;
      text-align: center;
      backdrop-filter: blur(10px);
    `;
    messageDiv.textContent = message;
    
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
      document.body.removeChild(messageDiv);
    }, 3000);
  }

  // 添加事件监听器的便捷方法
  on(event: string, callback: (data: any) => void): void {
    this.addEventListener(event, (e: any) => {
      callback(e.detail);
    });
  }
}
