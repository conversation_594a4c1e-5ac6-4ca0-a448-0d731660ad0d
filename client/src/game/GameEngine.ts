import * as THREE from 'three';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { Insect, BreedingBox, Player } from '../../../shared/types';

export class GameEngine extends EventTarget {
  private scene: THREE.Scene;
  private camera: THREE.PerspectiveCamera;
  private renderer: THREE.WebGLRenderer;
  private controls: OrbitControls;
  private fbxLoader: FBXLoader;
  
  private insectModels: Map<string, THREE.Group> = new Map();
  private breedingBoxModels: Map<string, THREE.Group> = new Map();
  private loadedFBXModels: Map<string, THREE.Group> = new Map();
  
  private raycaster: THREE.Raycaster;
  private mouse: THREE.Vector2;
  
  private gameState: {
    player?: Player;
    insects: Insect[];
    breedingBoxes: BreedingBox[];
  } = {
    insects: [],
    breedingBoxes: []
  };

  constructor() {
    super();
    this.scene = new THREE.Scene();
    this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    this.renderer = new THREE.WebGLRenderer({ 
      canvas: document.getElementById('gameCanvas') as HTMLCanvasElement,
      antialias: true 
    });
    this.fbxLoader = new FBXLoader();
    this.raycaster = new THREE.Raycaster();
    this.mouse = new THREE.Vector2();
  }

  async init(): Promise<void> {
    // 设置渲染器
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setClearColor(0x87CEEB); // 天蓝色背景
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

    // 设置相机位置
    this.camera.position.set(0, 10, 15);
    this.camera.lookAt(0, 0, 0);

    // 设置控制器
    this.controls = new OrbitControls(this.camera, this.renderer.domElement);
    this.controls.enableDamping = true;
    this.controls.dampingFactor = 0.05;
    this.controls.maxPolarAngle = Math.PI / 2; // 限制垂直旋转

    // 添加光照
    this.setupLighting();

    // 创建场景环境
    this.createEnvironment();

    // 加载FBX模型
    await this.loadFBXModels();

    // 设置事件监听
    this.setupEventListeners();

    console.log('GameEngine 初始化完成');
  }

  private setupLighting(): void {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    this.scene.add(ambientLight);

    // 主光源
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    this.scene.add(directionalLight);

    // 补充光源
    const fillLight = new THREE.DirectionalLight(0xffffff, 0.3);
    fillLight.position.set(-10, 5, -5);
    this.scene.add(fillLight);
  }

  private createEnvironment(): void {
    // 创建地面
    const groundGeometry = new THREE.PlaneGeometry(50, 50);
    const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x90EE90 });
    const ground = new THREE.Mesh(groundGeometry, groundMaterial);
    ground.rotation.x = -Math.PI / 2;
    ground.receiveShadow = true;
    this.scene.add(ground);

    // 添加一些装饰性元素
    this.createDecorations();
  }

  private createDecorations(): void {
    // 创建一些树木或装饰
    const treeGeometry = new THREE.CylinderGeometry(0.2, 0.3, 3);
    const treeMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
    
    for (let i = 0; i < 5; i++) {
      const tree = new THREE.Mesh(treeGeometry, treeMaterial);
      tree.position.set(
        (Math.random() - 0.5) * 40,
        1.5,
        (Math.random() - 0.5) * 40
      );
      tree.castShadow = true;
      this.scene.add(tree);

      // 树冠
      const crownGeometry = new THREE.SphereGeometry(1.5);
      const crownMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 });
      const crown = new THREE.Mesh(crownGeometry, crownMaterial);
      crown.position.copy(tree.position);
      crown.position.y += 2.5;
      crown.castShadow = true;
      this.scene.add(crown);
    }
  }

  private async loadFBXModels(): Promise<void> {
    try {
      console.log('开始加载FBX模型...');

      // 加载蟑螂模型
      const cockroachModel = await this.loadFBXModel('/models/cockroach.fbx');
      this.loadedFBXModels.set('cockroach', cockroachModel);
      console.log('蟑螂模型加载完成');

      // 加载甲虫模型
      const beetleModel = await this.loadFBXModel('/models/beetle.fbx');
      this.loadedFBXModels.set('beetle', beetleModel);
      console.log('甲虫模型加载完成');

      console.log('所有FBX模型加载完成');
    } catch (error) {
      console.error('加载FBX模型失败:', error);
      console.log('使用占位符模型');
      // 使用占位符模型作为后备
      this.createPlaceholderModels();
    }
  }

  private loadFBXModel(path: string): Promise<THREE.Group> {
    return new Promise((resolve, reject) => {
      this.fbxLoader.load(
        path,
        (fbx) => {
          // 设置模型属性
          fbx.scale.setScalar(0.01); // 通常FBX模型比较大，需要缩小
          fbx.traverse((child) => {
            if (child instanceof THREE.Mesh) {
              child.castShadow = true;
              child.receiveShadow = true;

              // 确保材质正确设置
              if (child.material) {
                if (Array.isArray(child.material)) {
                  child.material.forEach(mat => {
                    if (mat instanceof THREE.MeshBasicMaterial) {
                      // 转换为Lambert材质以支持光照
                      const lambertMat = new THREE.MeshLambertMaterial({
                        color: mat.color,
                        map: mat.map
                      });
                      child.material = lambertMat;
                    }
                  });
                } else if (child.material instanceof THREE.MeshBasicMaterial) {
                  const lambertMat = new THREE.MeshLambertMaterial({
                    color: child.material.color,
                    map: child.material.map
                  });
                  child.material = lambertMat;
                }
              }
            }
          });

          // 居中模型
          const box = new THREE.Box3().setFromObject(fbx);
          const center = box.getCenter(new THREE.Vector3());
          fbx.position.sub(center);

          resolve(fbx);
        },
        (progress) => {
          console.log('加载进度:', (progress.loaded / progress.total * 100) + '%');
        },
        (error) => {
          console.error('FBX加载错误:', error);
          reject(error);
        }
      );
    });
  }

  private createPlaceholderInsect(color: number, type: string): THREE.Group {
    const group = new THREE.Group();
    
    // 身体
    const bodyGeometry = new THREE.CapsuleGeometry(0.1, 0.3);
    const bodyMaterial = new THREE.MeshLambertMaterial({ color });
    const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
    body.castShadow = true;
    group.add(body);

    // 头部
    const headGeometry = new THREE.SphereGeometry(0.08);
    const head = new THREE.Mesh(headGeometry, bodyMaterial);
    head.position.z = 0.2;
    head.castShadow = true;
    group.add(head);

    // 腿部（简化）
    for (let i = 0; i < 6; i++) {
      const legGeometry = new THREE.CylinderGeometry(0.01, 0.01, 0.1);
      const leg = new THREE.Mesh(legGeometry, bodyMaterial);
      const angle = (i / 6) * Math.PI * 2;
      leg.position.x = Math.cos(angle) * 0.12;
      leg.position.y = -0.1;
      leg.position.z = Math.sin(angle) * 0.05;
      leg.rotation.z = Math.cos(angle) * 0.3;
      group.add(leg);
    }

    group.userData = { type, clickable: true };
    return group;
  }

  private createPlaceholderModels(): void {
    const cockroachModel = this.createPlaceholderInsect(0x8B4513, 'cockroach');
    this.loadedFBXModels.set('cockroach', cockroachModel);

    const beetleModel = this.createPlaceholderInsect(0x000000, 'beetle');
    this.loadedFBXModels.set('beetle', beetleModel);
  }

  private setupEventListeners(): void {
    // 鼠标点击事件
    this.renderer.domElement.addEventListener('click', (event) => {
      this.onMouseClick(event);
    });

    // 鼠标移动事件（用于悬停效果）
    this.renderer.domElement.addEventListener('mousemove', (event) => {
      this.onMouseMove(event);
    });
  }

  private onMouseClick(event: MouseEvent): void {
    // 计算鼠标位置
    const rect = this.renderer.domElement.getBoundingClientRect();
    this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
    this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

    // 射线检测
    this.raycaster.setFromCamera(this.mouse, this.camera);
    const intersects = this.raycaster.intersectObjects(this.scene.children, true);

    for (const intersect of intersects) {
      const object = intersect.object;
      let parent = object.parent;
      
      // 向上查找可点击的对象
      while (parent) {
        if (parent.userData.clickable) {
          if (parent.userData.type === 'insect') {
            this.dispatchEvent(new CustomEvent('insectClicked', { 
              detail: parent.userData.insectId 
            }));
          } else if (parent.userData.type === 'breedingBox') {
            this.dispatchEvent(new CustomEvent('breedingBoxClicked', { 
              detail: parent.userData.boxId 
            }));
          }
          return;
        }
        parent = parent.parent;
      }
    }
  }

  private onMouseMove(event: MouseEvent): void {
    // 这里可以添加鼠标悬停效果
    const rect = this.renderer.domElement.getBoundingClientRect();
    this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
    this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
  }

  updateGameState(gameState: any): void {
    this.gameState = gameState;
    this.updateScene();
  }

  private updateScene(): void {
    // 清除现有的昆虫和养殖箱模型
    this.clearModels();

    // 创建养殖箱
    this.createBreedingBoxes();

    // 创建昆虫
    this.createInsects();
  }

  private clearModels(): void {
    // 清除昆虫模型
    for (const [id, model] of this.insectModels) {
      this.scene.remove(model);
    }
    this.insectModels.clear();

    // 清除养殖箱模型
    for (const [id, model] of this.breedingBoxModels) {
      this.scene.remove(model);
    }
    this.breedingBoxModels.clear();
  }

  private createBreedingBoxes(): void {
    const boxSpacing = 8;
    let boxIndex = 0;

    for (const box of this.gameState.breedingBoxes) {
      const boxModel = this.createBreedingBoxModel(box);

      // 排列养殖箱
      const row = Math.floor(boxIndex / 3);
      const col = boxIndex % 3;
      boxModel.position.set(
        (col - 1) * boxSpacing,
        0,
        row * boxSpacing - 5
      );

      boxModel.userData = {
        type: 'breedingBox',
        boxId: box.id,
        clickable: true
      };

      this.scene.add(boxModel);
      this.breedingBoxModels.set(box.id, boxModel);
      boxIndex++;
    }
  }

  private createBreedingBoxModel(box: BreedingBox): THREE.Group {
    const group = new THREE.Group();

    // 养殖箱底座
    const baseGeometry = new THREE.BoxGeometry(4, 0.2, 3);
    const baseMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
    const base = new THREE.Mesh(baseGeometry, baseMaterial);
    base.position.y = 0.1;
    base.castShadow = true;
    base.receiveShadow = true;
    group.add(base);

    // 养殖箱边框
    const frameGeometry = new THREE.BoxGeometry(4.2, 1, 0.1);
    const frameMaterial = new THREE.MeshLambertMaterial({ color: 0x654321 });

    // 四个边框
    const frames = [
      { pos: [0, 0.5, 1.55], rot: [0, 0, 0] },
      { pos: [0, 0.5, -1.55], rot: [0, 0, 0] },
      { pos: [2.05, 0.5, 0], rot: [0, Math.PI/2, 0] },
      { pos: [-2.05, 0.5, 0], rot: [0, Math.PI/2, 0] }
    ];

    frames.forEach(frame => {
      const frameMesh = new THREE.Mesh(frameGeometry, frameMaterial);
      frameMesh.position.set(...frame.pos);
      frameMesh.rotation.set(...frame.rot);
      frameMesh.castShadow = true;
      group.add(frameMesh);
    });

    // 添加标签
    this.addBoxLabel(group, box.name);

    return group;
  }

  private addBoxLabel(group: THREE.Group, text: string): void {
    // 创建文本纹理
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d')!;
    canvas.width = 256;
    canvas.height = 64;

    context.fillStyle = '#ffffff';
    context.fillRect(0, 0, canvas.width, canvas.height);
    context.fillStyle = '#000000';
    context.font = '20px Arial';
    context.textAlign = 'center';
    context.fillText(text, canvas.width / 2, canvas.height / 2 + 7);

    const texture = new THREE.CanvasTexture(canvas);
    const labelMaterial = new THREE.MeshBasicMaterial({ map: texture });
    const labelGeometry = new THREE.PlaneGeometry(2, 0.5);
    const label = new THREE.Mesh(labelGeometry, labelMaterial);

    label.position.set(0, 1.2, 0);
    label.rotation.x = -Math.PI / 6; // 稍微倾斜以便观看
    group.add(label);
  }

  private createInsects(): void {
    for (const insect of this.gameState.insects) {
      const breedingBox = this.gameState.breedingBoxes.find(box => box.id === insect.breedingBoxId);
      if (!breedingBox) continue;

      const boxModel = this.breedingBoxModels.get(breedingBox.id);
      if (!boxModel) continue;

      const insectModel = this.createInsectModel(insect);

      // 在养殖箱内随机放置昆虫
      const boxPosition = boxModel.position;
      const randomX = (Math.random() - 0.5) * 3;
      const randomZ = (Math.random() - 0.5) * 2;

      insectModel.position.set(
        boxPosition.x + randomX,
        0.3,
        boxPosition.z + randomZ
      );

      // 随机旋转
      insectModel.rotation.y = Math.random() * Math.PI * 2;

      insectModel.userData = {
        type: 'insect',
        insectId: insect.id,
        clickable: true
      };

      this.scene.add(insectModel);
      this.insectModels.set(insect.id, insectModel);
    }
  }

  private createInsectModel(insect: Insect): THREE.Group {
    // 根据昆虫类型获取基础模型
    const baseModel = this.loadedFBXModels.get(insect.type);
    if (!baseModel) {
      console.warn(`未找到${insect.type}模型，使用占位符`);
      return this.createPlaceholderInsect(0x8B4513, insect.type);
    }

    // 深度克隆模型
    const model = this.cloneFBXModel(baseModel);

    // 根据生命阶段调整大小和颜色
    this.adjustInsectAppearance(model, insect);

    return model;
  }

  private cloneFBXModel(original: THREE.Group): THREE.Group {
    const cloned = original.clone(true);

    // 确保材质也被正确克隆
    cloned.traverse((child) => {
      if (child instanceof THREE.Mesh && child.material) {
        if (Array.isArray(child.material)) {
          child.material = child.material.map(mat => mat.clone());
        } else {
          child.material = child.material.clone();
        }
      }
    });

    return cloned;
  }

  private adjustInsectAppearance(model: THREE.Group, insect: Insect): void {
    let scale = 1;
    let colorMultiplier = 1;

    switch (insect.stage) {
      case 'egg':
        scale = 0.2;
        // 卵的特殊外观 - 更圆润，奶白色
        model.traverse((child) => {
          if (child instanceof THREE.Mesh) {
            const material = child.material as THREE.MeshLambertMaterial;
            if (material) {
              material.color.setHex(0xFFFACD); // 奶白色
              material.transparent = true;
              material.opacity = 0.9;
            }
          }
        });
        break;
      case 'larva':
        scale = 0.4;
        // 幼虫 - 稍微发白，柔软的感觉
        model.traverse((child) => {
          if (child instanceof THREE.Mesh) {
            const material = child.material as THREE.MeshLambertMaterial;
            if (material) {
              material.color.multiplyScalar(1.2); // 稍微变亮
            }
          }
        });
        break;
      case 'pupa':
        scale = 0.7;
        // 蛹 - 棕色，硬壳的感觉
        model.traverse((child) => {
          if (child instanceof THREE.Mesh) {
            const material = child.material as THREE.MeshLambertMaterial;
            if (material) {
              material.color.setHex(0x8B4513); // 棕色
              material.transparent = true;
              material.opacity = 0.95;
            }
          }
        });
        break;
      case 'adult':
        scale = 1.0;
        // 成虫保持原始颜色
        break;
    }

    // 应用缩放
    model.scale.setScalar(scale);

    // 根据健康状况调整外观
    if (insect.health < 50) {
      colorMultiplier = 0.6; // 不健康时变暗
    } else if (insect.health < 30) {
      colorMultiplier = 0.4; // 非常不健康时更暗
    }

    if (colorMultiplier < 1) {
      model.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          const material = child.material as THREE.MeshLambertMaterial;
          if (material && insect.stage === 'adult') { // 只对成虫应用健康状况颜色
            material.color.multiplyScalar(colorMultiplier);
          }
        }
      });
    }

    // 根据饥饿程度添加视觉效果
    if (insect.hunger < 20 && insect.stage !== 'egg') {
      model.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          const material = child.material as THREE.MeshLambertMaterial;
          if (material) {
            // 饥饿时稍微透明
            material.transparent = true;
            material.opacity = 0.8;
          }
        }
      });
    }
  }

  handleResize(): void {
    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(window.innerWidth, window.innerHeight);
  }

  start(): void {
    this.animate();
  }

  private animate(): void {
    requestAnimationFrame(() => this.animate());

    // 更新控制器
    this.controls.update();

    // 添加一些动画效果
    this.updateAnimations();

    // 渲染场景
    this.renderer.render(this.scene, this.camera);
  }

  private updateAnimations(): void {
    const time = Date.now() * 0.001;

    // 让昆虫根据生命阶段有不同的动画
    for (const [id, model] of this.insectModels) {
      const insect = this.gameState.insects.find(i => i.id === id);
      if (!insect) continue;

      const idHash = this.hashString(id); // 为每个昆虫创建唯一的动画偏移
      const baseY = 0.3;

      switch (insect.stage) {
        case 'egg':
          // 卵基本不动，只有轻微的"呼吸"效果
          const breathScale = 1 + Math.sin(time * 2 + idHash) * 0.02;
          model.scale.setScalar(0.2 * breathScale);
          break;

        case 'larva':
          // 幼虫有蠕动效果
          model.position.y = baseY + Math.sin(time * 3 + idHash) * 0.02;
          model.rotation.z = Math.sin(time * 2 + idHash) * 0.1;
          break;

        case 'pupa':
          // 蛹有轻微摆动
          model.rotation.x = Math.sin(time * 1.5 + idHash) * 0.05;
          model.rotation.z = Math.cos(time * 1.2 + idHash) * 0.03;
          break;

        case 'adult':
          // 成虫最活跃，会走动和转向
          if (insect.health > 50 && insect.hunger > 30) {
            // 健康的成虫会移动
            model.position.y = baseY + Math.sin(time * 4 + idHash) * 0.08;
            model.rotation.y += Math.sin(time * 0.5 + idHash) * 0.02;

            // 偶尔改变朝向
            if (Math.sin(time * 0.1 + idHash) > 0.95) {
              model.rotation.y += (Math.random() - 0.5) * 0.5;
            }
          } else {
            // 不健康的成虫动作缓慢
            model.position.y = baseY + Math.sin(time * 1 + idHash) * 0.02;
          }
          break;
      }

      // 根据快乐度调整动画速度
      const happinessMultiplier = insect.happiness / 100;
      if (happinessMultiplier < 0.5) {
        // 不快乐的昆虫动作更慢
        model.position.y *= 0.5;
        model.rotation.y *= 0.5;
      }
    }

    // 让养殖箱标签轻微摆动
    for (const [id, boxModel] of this.breedingBoxModels) {
      const label = boxModel.children.find(child =>
        child instanceof THREE.Mesh && child.geometry instanceof THREE.PlaneGeometry
      );
      if (label) {
        label.rotation.x = -Math.PI / 6 + Math.sin(time * 0.5) * 0.02;
      }
    }
  }

  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash) / 1000000; // 归一化到较小的数值
  }
}
